<?php
namespace App\Controllers;

use App\Models\User;
use App\Core\Controller;
use App\Core\Security;

class AuthController extends Controller {
    private $user;
    
    public function __construct($db) {
        parent::__construct(); // Chiama il costruttore del Controller padre
        $this->user = new User($db);
    }
    
    public function showLoginForm() {
        // Se l'utente è già loggato, redirect alla dashboard
        if (isset($_SESSION['user'])) {
            header('Location: ' . BASE_URL . 'dashboard');
            exit;
        }

        $this->view('auth/login', ['pageTitle' => 'Login']);
    }

    public function login() {
        error_log("AuthController::login() chiamato - Metodo: " . $_SERVER['REQUEST_METHOD']);
        file_put_contents('logs/login_test.log', date('Y-m-d H:i:s') . " - AuthController::login() chiamato - Metodo: " . $_SERVER['REQUEST_METHOD'] . "\n", FILE_APPEND);

        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            error_log("POST request ricevuto per login");
            file_put_contents('logs/login_test.log', date('Y-m-d H:i:s') . " - POST request ricevuto per login\n", FILE_APPEND);

            // Verifica CSRF token (temporaneamente disabilitata per test)
            /*if (!$this->verificaCsrf()) {
                error_log("AuthController::login() - Token CSRF non valido");
                file_put_contents('logs/login_test.log', date('Y-m-d H:i:s') . " - Token CSRF non valido\n", FILE_APPEND);
                $_SESSION['error'] = 'Token di sicurezza non valido. Riprova.';
                $this->redirect('login');
                return;
            }*/
            error_log("CSRF verificato con successo (disabilitato per test)");
            file_put_contents('logs/login_test.log', date('Y-m-d H:i:s') . " - CSRF verificato con successo (disabilitato per test)\n", FILE_APPEND);

            // Utilizziamo sanitizeInput invece di htmlspecialchars manuale
            $username = isset($_POST['username']) ? Security::sanitizeInput(trim($_POST['username'])) : '';
            $password = $_POST['password'] ?? '';

            error_log("Tentativo di login per username: " . $username);
            error_log("Password fornita: " . (empty($password) ? 'VUOTA' : 'PRESENTE'));

            if (empty($username) || empty($password)) {
                $_SESSION['error'] = 'Inserisci username e password';
                error_log("Login fallito: campi vuoti");
                $this->view('auth/login', ['pageTitle' => 'Login']);
                return;
            }

            $user = $this->user->login($username, $password);
            error_log("Risultato autenticazione: " . ($user ? 'SUCCESSO' : 'FALLIMENTO'));
            file_put_contents('logs/login_test.log', date('Y-m-d H:i:s') . " - Risultato autenticazione: " . ($user ? 'SUCCESSO' : 'FALLIMENTO') . "\n", FILE_APPEND);

            if ($user) {
                $_SESSION['user'] = $user;
                $_SESSION['success'] = 'Login effettuato con successo';
                error_log("Login riuscito per l'utente: " . $username . " con ruolo: " . $user['role']);
                error_log("Sessione creata: " . print_r($_SESSION['user'], true));

                // Reindirizza in base al ruolo
                if ($user['role'] === 'admin') {
                    error_log("Redirect a admin dashboard");
                    header('Location: ' . BASE_URL . 'admin');
                } else {
                    error_log("Redirect a dashboard normale");
                    header('Location: ' . BASE_URL . 'dashboard');
                }
                exit;
            }

            error_log("Login fallito: credenziali non valide per username: " . $username);
            $_SESSION['error'] = 'Credenziali non valide';
            $this->view('auth/login', ['pageTitle' => 'Login']);
            return;
        }

        error_log("GET request - Mostra form di login");
        $this->view('auth/login', ['pageTitle' => 'Login']);
    }
    
    public function logout() {
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        session_unset();
        session_destroy();
        header('Location: ' . BASE_URL . 'login');
        exit;
    }
    
    public function isAuthenticated() {
        return isset($_SESSION['user']) && !empty($_SESSION['user']['role']);
    }

    public function isAdmin() {
        return $this->isAuthenticated() && $_SESSION['user']['role'] === 'admin';
    }
    
    // Middleware per proteggere le rotte
    public function requireAuth() {
        if (!$this->isAuthenticated()) {
            $_SESSION['error'] = 'Accesso negato. Effettua il login.';
            header('Location: ' . BASE_URL . 'login');
            exit;
        }
    }
    
    public function requireAdmin() {
        if (!$this->isAdmin()) {
            $_SESSION['error'] = 'Accesso negato. Richiesti privilegi di amministratore.';
            header('Location: ' . BASE_URL . 'dashboard');
            exit;
        }
    }
    
    public function profile() {
        if (!isset($_SESSION['user'])) {
            header('Location: ' . BASE_URL . 'login');
            exit;
        }

        $this->view('auth/profile', ['pageTitle' => 'Profilo Utente']);
    }
}
