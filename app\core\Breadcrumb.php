<?php
/**
 * Breadcrumb - Sistema di Navigazione Dinamica
 * Studio Tecnico - Generazione Automatica Breadcrumb
 */

namespace App\Core;

class Breadcrumb {
    private static $breadcrumbs = [];
    private static $routeMap = [];
    
    /**
     * Inizializza la mappa delle route per i breadcrumb
     */
    public static function init() {
        self::$routeMap = [
            // Dashboard
            '' => ['title' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
            'dashboard' => ['title' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
            
            // Clienti
            'clienti' => ['title' => 'Clienti', 'icon' => 'fas fa-users'],
            'clienti/create' => ['title' => 'Nuovo Cliente', 'icon' => 'fas fa-user-plus', 'parent' => 'clienti'],
            'clienti/edit' => ['title' => 'Modifica Cliente', 'icon' => 'fas fa-user-edit', 'parent' => 'clienti'],
            'clienti/view' => ['title' => 'Dettagli Cliente', 'icon' => 'fas fa-user', 'parent' => 'clienti'],
            
            // Progetti
            'progetti' => ['title' => 'Progetti', 'icon' => 'fas fa-project-diagram'],
            'progetti/create' => ['title' => 'Nuovo Progetto', 'icon' => 'fas fa-plus-circle', 'parent' => 'progetti'],
            'progetti/edit' => ['title' => 'Modifica Progetto', 'icon' => 'fas fa-edit', 'parent' => 'progetti'],
            'progetti/view' => ['title' => 'Dettagli Progetto', 'icon' => 'fas fa-eye', 'parent' => 'progetti'],
            
            // Pratiche
            'pratiche' => ['title' => 'Pratiche', 'icon' => 'fas fa-folder-open'],
            'pratiche/create' => ['title' => 'Nuova Pratica', 'icon' => 'fas fa-file-plus', 'parent' => 'pratiche'],
            'pratiche/edit' => ['title' => 'Modifica Pratica', 'icon' => 'fas fa-file-edit', 'parent' => 'pratiche'],
            'pratiche/view' => ['title' => 'Dettagli Pratica', 'icon' => 'fas fa-file-alt', 'parent' => 'pratiche'],
            'pratiche/dettagli' => ['title' => 'Dettagli Pratica', 'icon' => 'fas fa-file-alt', 'parent' => 'pratiche'],
            'pratiche/statistiche' => ['title' => 'Statistiche Pratiche', 'icon' => 'fas fa-chart-bar', 'parent' => 'pratiche'],
            
            // Scadenze
            'scadenze' => ['title' => 'Scadenze', 'icon' => 'fas fa-clock'],
            'scadenze/calendario' => ['title' => 'Calendario Scadenze', 'icon' => 'fas fa-calendar-alt', 'parent' => 'scadenze'],
            
            // Notifiche
            'notifiche' => ['title' => 'Notifiche', 'icon' => 'fas fa-bell'],
            'notifiche/settings' => ['title' => 'Impostazioni Notifiche', 'icon' => 'fas fa-cog', 'parent' => 'notifiche'],
            
            // Admin
            'admin' => ['title' => 'Amministrazione', 'icon' => 'fas fa-cogs'],
            'admin/users' => ['title' => 'Gestione Utenti', 'icon' => 'fas fa-users-cog', 'parent' => 'admin'],
            'admin/backup' => ['title' => 'Backup Sistema', 'icon' => 'fas fa-download', 'parent' => 'admin'],
            'admin/logs' => ['title' => 'Log Sistema', 'icon' => 'fas fa-file-alt', 'parent' => 'admin'],
            'admin/config' => ['title' => 'Configurazione', 'icon' => 'fas fa-sliders-h', 'parent' => 'admin'],
            'admin/profile/setup' => ['title' => 'Setup Profilo', 'icon' => 'fas fa-user-cog', 'parent' => 'admin'],
            
            // Auth
            'login' => ['title' => 'Accesso', 'icon' => 'fas fa-sign-in-alt'],
            'logout' => ['title' => 'Disconnessione', 'icon' => 'fas fa-sign-out-alt'],
            'profilo' => ['title' => 'Profilo Utente', 'icon' => 'fas fa-user-circle'],
        ];
    }
    
    /**
     * Genera automaticamente i breadcrumb basandosi sulla route corrente
     */
    public static function generate($currentRoute = null, $params = []) {
        if (self::$routeMap === []) {
            self::init();
        }
        
        if ($currentRoute === null) {
            $currentRoute = self::getCurrentRoute();
        }
        
        // Pulisci la route dai parametri
        $cleanRoute = self::cleanRoute($currentRoute);
        
        // Genera i breadcrumb
        $breadcrumbs = [];
        
        // Aggiungi sempre Home/Dashboard come primo elemento
        if ($cleanRoute !== '' && $cleanRoute !== 'dashboard') {
            $breadcrumbs[] = [
                'title' => 'Dashboard',
                'url' => BASE_URL . 'dashboard',
                'icon' => 'fas fa-tachometer-alt',
                'active' => false
            ];
        }
        
        // Costruisci la catena di breadcrumb
        $chain = self::buildBreadcrumbChain($cleanRoute);
        
        foreach ($chain as $index => $routeKey) {
            $routeInfo = self::$routeMap[$routeKey] ?? null;
            
            if ($routeInfo) {
                $isLast = ($index === count($chain) - 1);
                
                $breadcrumb = [
                    'title' => $routeInfo['title'],
                    'url' => $isLast ? null : BASE_URL . $routeKey,
                    'icon' => $routeInfo['icon'] ?? 'fas fa-circle',
                    'active' => $isLast
                ];
                
                // Aggiungi parametri dinamici al titolo se disponibili
                if (!empty($params) && $isLast) {
                    $breadcrumb['title'] = self::enhanceTitle($breadcrumb['title'], $params, $routeKey);
                }
                
                $breadcrumbs[] = $breadcrumb;
            }
        }
        
        return $breadcrumbs;
    }
    
    /**
     * Costruisce la catena di breadcrumb risalendo i parent
     */
    private static function buildBreadcrumbChain($route) {
        $chain = [];
        $currentRoute = $route;
        
        while ($currentRoute && isset(self::$routeMap[$currentRoute])) {
            $chain[] = $currentRoute;
            $currentRoute = self::$routeMap[$currentRoute]['parent'] ?? null;
        }
        
        return array_reverse($chain);
    }
    
    /**
     * Pulisce la route dai parametri numerici
     */
    private static function cleanRoute($route) {
        // Rimuovi parametri numerici dalla fine
        $parts = explode('/', trim($route, '/'));
        $cleanParts = [];
        
        foreach ($parts as $part) {
            if (is_numeric($part)) {
                // Se è un numero, sostituisci con il pattern generico
                if (count($cleanParts) > 0) {
                    $lastPart = end($cleanParts);
                    if ($lastPart === 'clienti') {
                        $cleanParts[] = 'view';
                    } elseif ($lastPart === 'progetti') {
                        $cleanParts[] = 'view';
                    } elseif ($lastPart === 'pratiche') {
                        $cleanParts[] = 'dettagli';
                    }
                }
            } else {
                $cleanParts[] = $part;
            }
        }
        
        return implode('/', $cleanParts);
    }
    
    /**
     * Migliora il titolo con informazioni dinamiche
     */
    private static function enhanceTitle($title, $params, $route) {
        // Se abbiamo parametri, prova a migliorare il titolo
        if (!empty($params) && isset($params[0])) {
            $id = $params[0];
            
            // Prova a recuperare informazioni aggiuntive dal database
            try {
                $db = \App\Config\Database::getInstance();
                
                if (strpos($route, 'clienti') !== false) {
                    $stmt = $db->prepare("SELECT nome, cognome FROM clienti WHERE id = ?");
                    $stmt->execute([$id]);
                    $cliente = $stmt->fetch(\PDO::FETCH_ASSOC);
                    if ($cliente) {
                        return $title . ': ' . $cliente['nome'] . ' ' . $cliente['cognome'];
                    }
                } elseif (strpos($route, 'progetti') !== false) {
                    $stmt = $db->prepare("SELECT nome FROM progetti WHERE id = ?");
                    $stmt->execute([$id]);
                    $progetto = $stmt->fetch(\PDO::FETCH_ASSOC);
                    if ($progetto) {
                        return $title . ': ' . $progetto['nome'];
                    }
                } elseif (strpos($route, 'pratiche') !== false) {
                    $stmt = $db->prepare("SELECT numero_pratica, tipo_documento FROM pratiche WHERE id = ?");
                    $stmt->execute([$id]);
                    $pratica = $stmt->fetch(\PDO::FETCH_ASSOC);
                    if ($pratica) {
                        return $title . ': ' . $pratica['numero_pratica'] . ' (' . $pratica['tipo_documento'] . ')';
                    }
                }
            } catch (\Exception $e) {
                error_log("Errore nel recupero dati per breadcrumb: " . $e->getMessage());
            }
        }
        
        return $title;
    }
    
    /**
     * Ottiene la route corrente
     */
    private static function getCurrentRoute() {
        $uri = $_SERVER['REQUEST_URI'] ?? '';
        $path = parse_url($uri, PHP_URL_PATH);
        
        // Rimuovi il base path del progetto
        $basePath = '/progetti/studio_tecnico/';
        if (strpos($path, $basePath) === 0) {
            $path = substr($path, strlen($basePath));
        }
        
        return trim($path, '/');
    }
    
    /**
     * Aggiunge un breadcrumb personalizzato
     */
    public static function add($title, $url = null, $icon = null) {
        self::$breadcrumbs[] = [
            'title' => $title,
            'url' => $url,
            'icon' => $icon ?? 'fas fa-circle',
            'active' => $url === null
        ];
    }
    
    /**
     * Ottiene tutti i breadcrumb
     */
    public static function get() {
        return self::$breadcrumbs;
    }
    
    /**
     * Pulisce i breadcrumb
     */
    public static function clear() {
        self::$breadcrumbs = [];
    }
    
    /**
     * Renderizza i breadcrumb come HTML
     */
    public static function render($breadcrumbs = null) {
        if ($breadcrumbs === null) {
            $breadcrumbs = self::$breadcrumbs;
        }
        
        if (empty($breadcrumbs)) {
            return '';
        }
        
        $html = '<nav aria-label="breadcrumb" class="breadcrumb-nav">';
        $html .= '<ol class="breadcrumb-modern">';
        
        foreach ($breadcrumbs as $index => $breadcrumb) {
            $isLast = ($index === count($breadcrumbs) - 1);
            $classes = ['breadcrumb-item-modern'];
            
            if ($breadcrumb['active'] || $isLast) {
                $classes[] = 'active';
            }
            
            $html .= '<li class="' . implode(' ', $classes) . '">';
            
            if ($breadcrumb['icon']) {
                $html .= '<i class="' . $breadcrumb['icon'] . '"></i>';
            }
            
            if ($breadcrumb['url'] && !$breadcrumb['active']) {
                $html .= '<a href="' . htmlspecialchars($breadcrumb['url']) . '">';
                $html .= htmlspecialchars($breadcrumb['title']);
                $html .= '</a>';
            } else {
                $html .= '<span>' . htmlspecialchars($breadcrumb['title']) . '</span>';
            }
            
            $html .= '</li>';
        }
        
        $html .= '</ol>';
        $html .= '</nav>';
        
        return $html;
    }
}
