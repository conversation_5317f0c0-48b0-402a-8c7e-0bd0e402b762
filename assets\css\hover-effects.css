/**
 * Hover Effects - Effetti Hover Avanzati
 * Studio Tecnico - Micro-Interazioni Sofisticate
 */

/* === EFFETTI BASE === */
.hover-lift {
    transition: all var(--transition-base);
    transform: translateZ(0);
}

.hover-lift:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: var(--shadow-lg);
}

.hover-glow {
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.hover-glow::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-base);
    z-index: -1;
    border-radius: inherit;
}

.hover-glow:hover::before {
    opacity: 0.1;
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(99, 102, 241, 0.3);
}

/* === EFFETTI BOTTONI === */
.btn-hover-slide {
    position: relative;
    overflow: hidden;
    transition: all var(--transition-base);
}

.btn-hover-slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-slow);
    z-index: 1;
}

.btn-hover-slide:hover::before {
    left: 100%;
}

.btn-hover-scale {
    transition: all var(--transition-base);
}

.btn-hover-scale:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

.btn-hover-rotate {
    transition: all var(--transition-base);
}

.btn-hover-rotate:hover {
    transform: rotate(2deg) scale(1.02);
}

.btn-hover-bounce {
    transition: all var(--transition-base);
}

.btn-hover-bounce:hover {
    animation: bounce 0.6s ease;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    80% {
        transform: translateY(-5px);
    }
}

/* === EFFETTI CARD === */
.card-hover-float {
    transition: all var(--transition-base);
    transform: translateZ(0);
}

.card-hover-float:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.card-hover-tilt {
    transition: all var(--transition-base);
    transform-style: preserve-3d;
}

.card-hover-tilt:hover {
    transform: perspective(1000px) rotateX(5deg) rotateY(5deg);
    box-shadow: var(--shadow-xl);
}

.card-hover-expand {
    transition: all var(--transition-base);
    overflow: hidden;
}

.card-hover-expand:hover {
    transform: scale(1.03);
}

.card-hover-expand .card-content {
    transition: all var(--transition-base);
}

.card-hover-expand:hover .card-content {
    transform: scale(0.97);
}

/* === EFFETTI IMMAGINI === */
.img-hover-zoom {
    overflow: hidden;
    border-radius: var(--radius-lg);
}

.img-hover-zoom img {
    transition: transform var(--transition-slow);
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.img-hover-zoom:hover img {
    transform: scale(1.1);
}

.img-hover-overlay {
    position: relative;
    overflow: hidden;
    border-radius: var(--radius-lg);
}

.img-hover-overlay::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-base);
    z-index: 1;
}

.img-hover-overlay:hover::after {
    opacity: 0.3;
}

.img-hover-overlay .overlay-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    text-align: center;
    opacity: 0;
    transition: opacity var(--transition-base);
    z-index: 2;
}

.img-hover-overlay:hover .overlay-content {
    opacity: 1;
}

/* === EFFETTI LINK === */
.link-hover-underline {
    position: relative;
    text-decoration: none;
    transition: color var(--transition-base);
}

.link-hover-underline::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-base);
}

.link-hover-underline:hover::after {
    width: 100%;
}

.link-hover-background {
    position: relative;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    text-decoration: none;
    transition: all var(--transition-base);
    overflow: hidden;
}

.link-hover-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: left var(--transition-base);
    z-index: -1;
}

.link-hover-background:hover::before {
    left: 0;
}

.link-hover-background:hover {
    color: white;
    transform: translateY(-1px);
}

/* === EFFETTI ICONE === */
.icon-hover-spin {
    transition: transform var(--transition-base);
}

.icon-hover-spin:hover {
    transform: rotate(360deg);
}

.icon-hover-bounce {
    transition: transform var(--transition-base);
}

.icon-hover-bounce:hover {
    animation: iconBounce 0.6s ease;
}

@keyframes iconBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.2); }
}

.icon-hover-shake {
    transition: transform var(--transition-base);
}

.icon-hover-shake:hover {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.icon-hover-pulse {
    transition: all var(--transition-base);
}

.icon-hover-pulse:hover {
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* === EFFETTI TABELLE === */
.table-row-hover {
    transition: all var(--transition-base);
    cursor: pointer;
}

.table-row-hover:hover {
    background: var(--primary-50);
    transform: scale(1.01);
    box-shadow: var(--shadow-sm);
    z-index: 10;
    position: relative;
}

.table-cell-hover {
    transition: all var(--transition-base);
    position: relative;
}

.table-cell-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--primary-100);
    transition: width var(--transition-base);
    z-index: -1;
}

.table-cell-hover:hover::before {
    width: 100%;
}

/* === EFFETTI FORM === */
.input-hover-focus {
    transition: all var(--transition-base);
    border: 2px solid var(--neutral-200);
}

.input-hover-focus:hover {
    border-color: var(--primary-300);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.input-hover-focus:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    outline: none;
}

.label-hover-highlight {
    transition: all var(--transition-base);
    cursor: pointer;
}

.label-hover-highlight:hover {
    color: var(--primary-600);
    transform: translateX(2px);
}

/* === EFFETTI NAVBAR === */
.nav-item-hover {
    position: relative;
    transition: all var(--transition-base);
}

.nav-item-hover::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all var(--transition-base);
    transform: translateX(-50%);
}

.nav-item-hover:hover::after {
    width: 100%;
}

.nav-item-hover:hover {
    transform: translateY(-1px);
}

/* === EFFETTI DROPDOWN === */
.dropdown-hover {
    transition: all var(--transition-base);
}

.dropdown-hover:hover {
    background: var(--primary-50);
    transform: translateX(5px);
}

.dropdown-hover::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: var(--gradient-primary);
    transform: scaleY(0);
    transition: transform var(--transition-base);
}

.dropdown-hover:hover::before {
    transform: scaleY(1);
}

/* === EFFETTI BADGE === */
.badge-hover-grow {
    transition: all var(--transition-base);
}

.badge-hover-grow:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-sm);
}

.badge-hover-flip {
    transition: all var(--transition-base);
    transform-style: preserve-3d;
}

.badge-hover-flip:hover {
    transform: rotateY(180deg);
}

/* === EFFETTI PROGRESS BAR === */
.progress-hover-animate {
    overflow: hidden;
    position: relative;
}

.progress-hover-animate::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left var(--transition-slow);
}

.progress-hover-animate:hover::after {
    left: 100%;
}

/* === EFFETTI TOOLTIP === */
.tooltip-hover {
    position: relative;
}

.tooltip-hover::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--neutral-800);
    color: white;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    font-size: var(--text-xs);
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: all var(--transition-base);
    z-index: 1000;
}

.tooltip-hover::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--neutral-800);
    opacity: 0;
    pointer-events: none;
    transition: all var(--transition-base);
}

.tooltip-hover:hover::before,
.tooltip-hover:hover::after {
    opacity: 1;
    transform: translateX(-50%) translateY(-5px);
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .hover-lift:hover,
    .card-hover-float:hover,
    .card-hover-tilt:hover {
        transform: none;
        box-shadow: var(--shadow-md);
    }
    
    .table-row-hover:hover {
        transform: none;
    }
}

/* === TEMA SCURO === */
[data-theme="dark"] .table-row-hover:hover {
    background: var(--neutral-800);
}

[data-theme="dark"] .dropdown-hover:hover {
    background: var(--neutral-800);
}

[data-theme="dark"] .input-hover-focus:hover {
    border-color: var(--primary-400);
}

[data-theme="dark"] .tooltip-hover::before,
[data-theme="dark"] .tooltip-hover::after {
    background: var(--neutral-700);
    border-top-color: var(--neutral-700);
}

/* === PERFORMANCE === */
.hover-lift,
.card-hover-float,
.card-hover-tilt,
.btn-hover-scale,
.icon-hover-spin,
.table-row-hover {
    will-change: transform;
    transform: translateZ(0);
}

/* === ACCESSIBILITÀ === */
@media (prefers-reduced-motion: reduce) {
    .hover-lift:hover,
    .card-hover-float:hover,
    .card-hover-tilt:hover,
    .btn-hover-scale:hover,
    .btn-hover-rotate:hover,
    .icon-hover-spin:hover,
    .nav-item-hover:hover {
        transform: none;
        animation: none;
    }
    
    .btn-hover-bounce:hover,
    .icon-hover-bounce:hover,
    .icon-hover-shake:hover,
    .icon-hover-pulse:hover {
        animation: none;
    }
}

/* === STATI FOCUS === */
.hover-lift:focus,
.card-hover-float:focus,
.btn-hover-scale:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* === COMBINAZIONI === */
.hover-lift.hover-glow:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: var(--shadow-xl), 0 0 25px rgba(99, 102, 241, 0.4);
}

.card-hover-float.hover-glow:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-2xl), 0 0 30px rgba(99, 102, 241, 0.3);
}

/* === FORM ANIMATIONS === */
.form-animated {
    transition: all var(--transition-base);
}

.field-wrapper {
    position: relative;
    margin-bottom: var(--space-6);
    transition: all var(--transition-base);
}

.field-wrapper input,
.field-wrapper textarea,
.field-wrapper select {
    width: 100%;
    padding: var(--space-4) var(--space-5);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    font-size: var(--text-base);
    transition: all var(--transition-base);
    background: var(--surface-color);
    position: relative;
    z-index: 2;
}

.field-wrapper input:focus,
.field-wrapper textarea:focus,
.field-wrapper select:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* === FIELD INDICATORS === */
.field-indicator {
    position: absolute;
    top: 50%;
    right: var(--space-4);
    width: 20px;
    height: 20px;
    background: var(--success-500);
    border-radius: 50%;
    transform: translateY(-50%) scale(0);
    opacity: 0;
    transition: all var(--transition-base);
    z-index: 3;
}

.field-indicator::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: var(--text-xs);
    font-weight: bold;
}

.field-success .field-indicator {
    transform: translateY(-50%) scale(1);
    opacity: 1;
}

.field-error .field-indicator {
    background: var(--danger-500);
    transform: translateY(-50%) scale(1);
    opacity: 1;
}

.field-error .field-indicator::after {
    content: '✕';
}

/* === FOCUS RING === */
.field-focus-ring {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 2px solid var(--primary-500);
    border-radius: var(--radius-lg);
    transform: scale(0.95);
    opacity: 0;
    transition: all var(--transition-base);
    z-index: 1;
    pointer-events: none;
}

.field-focused .field-focus-ring {
    transform: scale(1);
    opacity: 0.3;
}

/* === FIELD STATES === */
.field-typing input,
.field-typing textarea {
    border-color: var(--primary-300);
    box-shadow: 0 0 0 1px rgba(99, 102, 241, 0.1);
}

.field-error input,
.field-error textarea,
.field-error select {
    border-color: var(--danger-500);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.field-success input,
.field-success textarea,
.field-success select {
    border-color: var(--success-500);
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

/* === FIELD MESSAGES === */
.field-message {
    position: absolute;
    bottom: -var(--space-5);
    left: 0;
    font-size: var(--text-xs);
    color: var(--danger-500);
    opacity: 0;
    transform: translateY(-10px);
    transition: all var(--transition-base);
    z-index: 4;
}

.field-message.error-message {
    opacity: 1;
    transform: translateY(0);
}

/* === FLOATING LABELS === */
.field-wrapper label {
    position: absolute;
    top: var(--space-4);
    left: var(--space-5);
    font-size: var(--text-base);
    color: var(--text-secondary);
    transition: all var(--transition-base);
    pointer-events: none;
    z-index: 3;
    background: var(--surface-color);
    padding: 0 var(--space-2);
}

.label-floating,
.field-has-value label {
    top: -var(--space-2);
    left: var(--space-3);
    font-size: var(--text-xs);
    color: var(--primary-600);
    font-weight: var(--font-semibold);
}

/* === FORM MESSAGES === */
.form-message {
    padding: var(--space-4) var(--space-5);
    border-radius: var(--radius-lg);
    margin-bottom: var(--space-6);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    opacity: 0;
    transform: translateY(-20px);
    transition: all var(--transition-base);
}

.form-message.success {
    background: var(--success-50);
    color: var(--success-700);
    border: 1px solid var(--success-200);
}

.form-message.error {
    background: var(--danger-50);
    color: var(--danger-700);
    border: 1px solid var(--danger-200);
}

/* === ANIMAZIONI KEYFRAMES === */
@keyframes fieldShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

@keyframes fieldPulse {
    0% { transform: translateY(-50%) scale(1); }
    50% { transform: translateY(-50%) scale(1.2); }
    100% { transform: translateY(-50%) scale(1); }
}

@keyframes formSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

@keyframes formError {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px); }
    75% { transform: translateX(10px); }
}

/* === SUBMIT BUTTON ANIMATIONS === */
.btn-submit-loading {
    position: relative;
    overflow: hidden;
}

.btn-submit-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: submitShimmer 1.5s infinite;
}

@keyframes submitShimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* === PROGRESS INDICATORS === */
.form-progress {
    width: 100%;
    height: 4px;
    background: var(--neutral-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--space-6);
}

.form-progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    width: 0%;
    transition: width var(--transition-base);
    position: relative;
}

.form-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: progressShimmer 1.5s infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* === STEP INDICATORS === */
.form-steps {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--space-8);
    position: relative;
}

.form-steps::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--neutral-200);
    z-index: 1;
}

.form-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-2);
    position: relative;
    z-index: 2;
}

.form-step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--neutral-200);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-bold);
    color: var(--text-secondary);
    transition: all var(--transition-base);
}

.form-step.active .form-step-circle {
    background: var(--gradient-primary);
    color: white;
    transform: scale(1.1);
    box-shadow: var(--shadow-md);
}

.form-step.completed .form-step-circle {
    background: var(--success-500);
    color: white;
}

.form-step.completed .form-step-circle::after {
    content: '✓';
    font-size: var(--text-sm);
}

.form-step-label {
    font-size: var(--text-xs);
    color: var(--text-secondary);
    text-align: center;
    transition: all var(--transition-base);
}

.form-step.active .form-step-label {
    color: var(--primary-600);
    font-weight: var(--font-semibold);
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .field-wrapper {
        margin-bottom: var(--space-4);
    }

    .field-wrapper input,
    .field-wrapper textarea,
    .field-wrapper select {
        padding: var(--space-3) var(--space-4);
        font-size: var(--text-sm);
    }

    .field-wrapper label {
        top: var(--space-3);
        left: var(--space-4);
        font-size: var(--text-sm);
    }

    .form-steps {
        margin-bottom: var(--space-6);
    }

    .form-step-circle {
        width: 32px;
        height: 32px;
        font-size: var(--text-sm);
    }

    .form-step-label {
        font-size: var(--text-xs);
    }
}

/* === TEMA SCURO === */
[data-theme="dark"] .field-wrapper input,
[data-theme="dark"] .field-wrapper textarea,
[data-theme="dark"] .field-wrapper select {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
    color: var(--neutral-200);
}

[data-theme="dark"] .field-wrapper label {
    background: var(--neutral-800);
    color: var(--neutral-400);
}

[data-theme="dark"] .label-floating,
[data-theme="dark"] .field-has-value label {
    color: var(--primary-400);
}

[data-theme="dark"] .form-message.success {
    background: var(--neutral-800);
    color: var(--success-400);
    border-color: var(--success-600);
}

[data-theme="dark"] .form-message.error {
    background: var(--neutral-800);
    color: var(--danger-400);
    border-color: var(--danger-600);
}

[data-theme="dark"] .form-progress {
    background: var(--neutral-600);
}

[data-theme="dark"] .form-step-circle {
    background: var(--neutral-600);
    color: var(--neutral-300);
}

[data-theme="dark"] .form-steps::before {
    background: var(--neutral-600);
}

/* === PERFORMANCE === */
.field-wrapper,
.field-indicator,
.field-focus-ring,
.form-step-circle {
    will-change: transform, opacity;
    transform: translateZ(0);
}

/* === ACCESSIBILITÀ === */
@media (prefers-reduced-motion: reduce) {
    .field-wrapper,
    .field-indicator,
    .field-focus-ring,
    .form-step-circle {
        transition-duration: 0.1s;
        animation: none;
    }

    .field-wrapper input:focus,
    .field-wrapper textarea:focus,
    .field-wrapper select:focus {
        transform: none;
    }
}
