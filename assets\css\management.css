/* === MANAGEMENT COMPONENTS MODERNIZZATI - STUDIO TECNICO === */

/* === MANAGEMENT CARDS === */
.management-card {
    background: var(--surface-color);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    height: 100%;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

.management-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.management-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.02) 0%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-base);
    pointer-events: none;
}

.management-card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-6px) scale(1.01);
    border-color: var(--primary-300);
}

.management-card:hover::before {
    opacity: 1;
}

.management-card:hover::after {
    opacity: 1;
}

/* === PAGE HEADER MODERNIZZATO === */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-8);
    padding: var(--space-6) var(--space-8);
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.page-title {
    font-size: var(--text-2xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    line-height: var(--leading-tight);
}

.page-title i {
    font-size: var(--text-xl);
    color: var(--primary-500);
    padding: var(--space-2);
    background: var(--primary-50);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
}

.page-title:hover i {
    transform: scale(1.1) rotate(5deg);
    background: var(--primary-100);
}

.page-actions {
    display: flex;
    gap: var(--space-3);
    align-items: center;
    flex-wrap: wrap;
}

/* === BOTTONI MANAGEMENT MODERNIZZATI === */
.btn {
    font-size: var(--text-sm);
    padding: var(--space-3) var(--space-5);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    border-radius: var(--radius-lg);
    font-weight: var(--font-medium);
    transition: all var(--transition-base);
    border: none;
    cursor: pointer;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn i {
    font-size: var(--text-base);
    transition: transform var(--transition-base);
}

.btn:hover i {
    transform: scale(1.1);
}

/* === BUTTON VARIANTS === */
.btn-neutral {
    background: var(--neutral-100);
    color: var(--neutral-700);
    border: 2px solid var(--neutral-200);
    box-shadow: var(--shadow-sm);
}

.btn-neutral:hover {
    background: var(--neutral-200);
    color: var(--neutral-800);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline-neutral {
    background: transparent;
    color: var(--neutral-600);
    border: 2px solid var(--neutral-300);
}

.btn-outline-neutral:hover {
    background: var(--neutral-100);
    color: var(--neutral-800);
    border-color: var(--neutral-400);
    transform: translateY(-1px);
}

.btn-success {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-danger {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-warning {
    background: var(--gradient-accent);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-warning:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-info {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
}

/* === BUTTON SIZES === */
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-xs);
    border-radius: var(--radius-md);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
    border-radius: var(--radius-xl);
}

/* === BUTTON GROUPS === */
.btn-group {
    display: flex;
    gap: 0;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.btn-group .btn {
    border-radius: 0;
    border-right: 1px solid var(--neutral-300);
}

.btn-group .btn:first-child {
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.btn-group .btn:last-child {
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    border-right: none;
}

/* === FORM MODERNIZZATI === */
.form-card {
    background: var(--surface-color);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-xl);
    padding: var(--space-8);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.form-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    opacity: 0.7;
}

.form-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.form-section {
    margin-bottom: var(--space-8);
    padding-bottom: var(--space-6);
    border-bottom: 1px solid var(--neutral-200);
    position: relative;
}

.form-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.form-section::before {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--gradient-primary);
    opacity: 0.5;
}

.form-section-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    position: relative;
}

.form-section-title i {
    color: var(--primary-500);
    font-size: var(--text-xl);
    padding: var(--space-2);
    background: var(--primary-50);
    border-radius: var(--radius-lg);
}

.form-section-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--gradient-primary);
}

/* === FORM CONTROLS MODERNIZZATI === */
.form-label {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-primary);
    margin-bottom: var(--space-2);
    display: block;
    position: relative;
}

.form-label.required::after {
    content: '*';
    color: var(--danger-color);
    margin-left: var(--space-1);
    font-weight: var(--font-bold);
}

.form-control {
    background: var(--surface-color);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-5);
    font-size: var(--text-base);
    color: var(--text-primary);
    transition: all var(--transition-base);
    width: 100%;
    position: relative;
}

.form-control:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
    background: var(--neutral-50);
    transform: translateY(-1px);
}

.form-control:hover {
    border-color: var(--primary-300);
}

.form-control::placeholder {
    color: var(--neutral-400);
    font-weight: var(--font-normal);
}

/* === FORM FLOATING LABELS === */
.form-floating {
    position: relative;
    margin-bottom: var(--space-6);
}

.form-floating .form-control {
    padding: var(--space-6) var(--space-5) var(--space-3);
    height: auto;
}

.form-floating label {
    position: absolute;
    top: 0;
    left: var(--space-5);
    height: 100%;
    padding: var(--space-6) 0 0;
    pointer-events: none;
    border: none;
    transform-origin: 0 0;
    transition: all var(--transition-base);
    color: var(--neutral-400);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
}

.form-floating .form-control:focus ~ label,
.form-floating .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.8;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
    color: var(--primary-600);
    font-weight: var(--font-medium);
}

/* === FORM VALIDATION === */
.form-control.is-valid {
    border-color: var(--success-color);
    background: var(--secondary-50);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
    background: #FEF2F2;
}

.valid-feedback {
    color: var(--success-color);
    font-size: var(--text-sm);
    margin-top: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.invalid-feedback {
    color: var(--danger-color);
    font-size: var(--text-sm);
    margin-top: var(--space-1);
    display: flex;
    align-items: center;
    gap: var(--space-1);
}

.valid-feedback i,
.invalid-feedback i {
    font-size: var(--text-xs);
}

/* === TABELLE MODERNIZZATE === */
.table-card {
    background: var(--surface-color);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    position: relative;
}

.table-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    opacity: 0.8;
}

.table-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.table {
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    background: var(--gradient-neutral);
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: var(--space-4) var(--space-6);
    border-bottom: 2px solid var(--neutral-200);
    position: relative;
    transition: all var(--transition-base);
}

.table thead th:hover {
    background: var(--primary-50);
    color: var(--primary-700);
}

.table thead th::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-base);
}

.table thead th:hover::after {
    width: 100%;
}

.table tbody td {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    padding: var(--space-5) var(--space-6);
    vertical-align: middle;
    border-bottom: 1px solid var(--neutral-200);
    transition: all var(--transition-base);
    position: relative;
}

.table tbody tr:last-child td {
    border-bottom: none;
}

.table tbody tr {
    transition: all var(--transition-base);
}

.table tbody tr:hover {
    background: var(--primary-50);
    transform: scale(1.01);
}

.table tbody tr:hover td {
    color: var(--text-primary);
}

/* === TABLE ACTIONS === */
.table-actions {
    display: flex;
    gap: var(--space-2);
    align-items: center;
    justify-content: flex-end;
}

.table-actions .btn {
    padding: var(--space-1) var(--space-3);
    font-size: var(--text-xs);
    border-radius: var(--radius-md);
}

/* === TABLE RESPONSIVE === */
.table-responsive {
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

@media (max-width: 768px) {
    .table-card {
        margin: 0 -var(--space-4);
        border-radius: 0;
        border-left: none;
        border-right: none;
    }

    .table thead th {
        padding: var(--space-3) var(--space-4);
        font-size: var(--text-xs);
    }

    .table tbody td {
        padding: var(--space-4);
        font-size: var(--text-xs);
    }

    .table-actions {
        flex-direction: column;
        gap: var(--space-1);
    }

    .table-actions .btn {
        width: 100%;
        justify-content: center;
    }
}

/* === DATATABLES INTEGRATION === */
.dataTables_wrapper {
    padding: var(--space-6);
    background: var(--surface-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
}

.dataTables_filter input {
    background: var(--surface-color);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    margin-left: var(--space-2);
    transition: all var(--transition-base);
}

.dataTables_filter input:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.dataTables_length select {
    background: var(--surface-color);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    padding: var(--space-2) var(--space-8) var(--space-2) var(--space-3);
    margin: 0 var(--space-2);
}

.dataTables_paginate .paginate_button {
    background: var(--neutral-100);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-md);
    padding: var(--space-2) var(--space-4);
    margin: 0 var(--space-1);
    color: var(--text-secondary);
    transition: all var(--transition-base);
}

.dataTables_paginate .paginate_button:hover {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
    transform: translateY(-1px);
}

.dataTables_paginate .paginate_button.current {
    background: var(--gradient-primary);
    border-color: var(--primary-500);
    color: white;
    box-shadow: var(--shadow-sm);
}

/* === TABELLE AVANZATE === */
.table-modern {
    border-collapse: separate;
    border-spacing: 0;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.table-modern thead th {
    background: var(--gradient-neutral);
    border-bottom: 2px solid var(--primary-200);
    font-weight: var(--font-semibold);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    font-size: var(--text-xs);
    padding: var(--space-4) var(--space-6);
    position: relative;
    transition: all var(--transition-base);
}

.table-modern thead th:hover {
    background: var(--primary-50);
    color: var(--primary-700);
}

.table-modern thead th.sorting:after,
.table-modern thead th.sorting_asc:after,
.table-modern thead th.sorting_desc:after {
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    right: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.7;
    transition: all var(--transition-base);
}

.table-modern thead th.sorting:after {
    content: '\f0dc'; /* fa-sort */
}

.table-modern thead th.sorting_asc:after {
    content: '\f0de'; /* fa-sort-up */
    color: var(--primary-600);
    opacity: 1;
}

.table-modern thead th.sorting_desc:after {
    content: '\f0dd'; /* fa-sort-down */
    color: var(--primary-600);
    opacity: 1;
}

.table-row-modern {
    transition: all var(--transition-base);
    border-bottom: 1px solid var(--neutral-200);
}

.table-row-modern:hover {
    background: var(--primary-50);
    transform: scale(1.01);
    box-shadow: var(--shadow-sm);
    z-index: 10;
    position: relative;
}

.table-row-modern td {
    padding: var(--space-5) var(--space-6);
    vertical-align: middle;
    border-bottom: none;
    transition: all var(--transition-base);
}

/* === CONTROLLI TABELLA === */
.table-controls {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    margin-bottom: var(--space-6);
    box-shadow: var(--shadow-sm);
}

.table-filters {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-4);
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: var(--space-1);
    min-width: 150px;
}

.filter-label {
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.date-range-filter {
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.date-separator {
    color: var(--neutral-400);
    font-weight: var(--font-medium);
}

.search-input-group {
    position: relative;
    display: flex;
}

.search-input-group .form-control {
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
    border-right: none;
}

.search-input-group .btn {
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
    border-left: none;
}

.table-actions {
    display: flex;
    gap: var(--space-2);
    justify-content: flex-end;
    align-items: end;
}

/* === DATATABLES WRAPPER MODERNIZZATO === */
.dataTables_wrapper {
    background: var(--surface-color);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
}

.dataTables_length {
    margin-bottom: var(--space-4);
}

.dataTables_length label {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.dataTables_length select {
    background: var(--surface-color);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    padding: var(--space-2) var(--space-8) var(--space-2) var(--space-3);
    font-size: var(--text-sm);
    transition: all var(--transition-base);
}

.dataTables_length select:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.dataTables_filter {
    margin-bottom: var(--space-4);
}

.dataTables_filter label {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.dataTables_filter input {
    background: var(--surface-color);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    font-size: var(--text-sm);
    transition: all var(--transition-base);
    width: 250px;
}

.dataTables_filter input:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
    transform: scale(1.02);
}

.dataTables_info {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    font-weight: var(--font-medium);
    padding: var(--space-4) 0;
}

/* === PAGINAZIONE MODERNIZZATA === */
.dataTables_paginate {
    padding: var(--space-4) 0;
}

.dataTables_paginate .pagination {
    margin: 0;
    gap: var(--space-1);
}

.dataTables_paginate .page-link {
    background: var(--neutral-100);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    transition: all var(--transition-base);
    margin: 0 var(--space-1);
}

.dataTables_paginate .page-link:hover {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.dataTables_paginate .page-item.active .page-link {
    background: var(--gradient-primary);
    border-color: var(--primary-500);
    color: white;
    box-shadow: var(--shadow-md);
}

.dataTables_paginate .page-item.disabled .page-link {
    opacity: 0.5;
    cursor: not-allowed;
}

/* === BUTTONS EXTENSION === */
.dt-buttons {
    margin-bottom: var(--space-4);
    display: flex;
    gap: var(--space-2);
    flex-wrap: wrap;
}

.dt-button {
    background: var(--neutral-100);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    color: var(--text-secondary);
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    transition: all var(--transition-base);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
}

.dt-button:hover {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.dt-button.buttons-collection {
    background: var(--gradient-primary);
    border-color: var(--primary-500);
    color: white;
}

.dt-button-collection {
    background: var(--surface-color);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--space-4);
}

.dt-button-collection .dt-button {
    width: 100%;
    justify-content: flex-start;
    margin-bottom: var(--space-2);
}

.dt-button-collection .dt-button:last-child {
    margin-bottom: 0;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .table-filters {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        min-width: auto;
    }

    .date-range-filter {
        flex-direction: column;
        gap: var(--space-2);
    }

    .table-actions {
        justify-content: flex-start;
        margin-top: var(--space-4);
    }

    .dataTables_wrapper {
        padding: var(--space-4);
    }

    .dataTables_filter input {
        width: 100%;
    }

    .dt-buttons {
        justify-content: center;
    }

    .table-modern thead th,
    .table-row-modern td {
        padding: var(--space-3) var(--space-4);
        font-size: var(--text-sm);
    }
}

/* === TEMA SCURO === */
[data-theme="dark"] .table-modern thead th {
    background: var(--neutral-800);
    color: var(--neutral-200);
}

[data-theme="dark"] .table-modern thead th:hover {
    background: var(--neutral-700);
    color: var(--primary-400);
}

[data-theme="dark"] .table-row-modern:hover {
    background: var(--neutral-800);
}

[data-theme="dark"] .dataTables_wrapper {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
}

[data-theme="dark"] .table-controls {
    background: var(--glass-bg);
    border-color: var(--glass-border);
}

[data-theme="dark"] .dataTables_filter input,
[data-theme="dark"] .dataTables_length select,
[data-theme="dark"] .filter-group .form-control,
[data-theme="dark"] .filter-group .form-select {
    background: var(--neutral-700);
    border-color: var(--neutral-600);
    color: var(--neutral-200);
}

[data-theme="dark"] .dt-button {
    background: var(--neutral-700);
    border-color: var(--neutral-600);
    color: var(--neutral-200);
}

[data-theme="dark"] .dt-button:hover {
    background: var(--primary-600);
    border-color: var(--primary-600);
    color: white;
}

/* === ALERT MODERNIZZATI === */
.alert {
    border: none;
    border-radius: var(--radius-xl);
    padding: var(--space-5) var(--space-6);
    margin-bottom: var(--space-6);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
}

.alert::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
    opacity: 0.8;
}

.alert:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.alert i {
    font-size: var(--text-lg);
    padding: var(--space-2);
    border-radius: var(--radius-lg);
    background: rgba(255, 255, 255, 0.2);
}

.alert-success {
    background: linear-gradient(135deg, #ECFDF5 0%, #D1FAE5 100%);
    color: #065F46;
    border: 1px solid #A7F3D0;
}

.alert-danger {
    background: linear-gradient(135deg, #FEF2F2 0%, #FECACA 100%);
    color: #991B1B;
    border: 1px solid #FCA5A5;
}

.alert-warning {
    background: linear-gradient(135deg, #FFFBEB 0%, #FEF3C7 100%);
    color: #92400E;
    border: 1px solid #FCD34D;
}

.alert-info {
    background: linear-gradient(135deg, #EFF6FF 0%, #DBEAFE 100%);
    color: #1E40AF;
    border: 1px solid #93C5FD;
}

.alert-primary {
    background: linear-gradient(135deg, #EEF2FF 0%, #E0E7FF 100%);
    color: #3730A3;
    border: 1px solid #C7D2FE;
}

/* === ALERT DISMISSIBLE === */
.alert-dismissible {
    padding-right: var(--space-12);
}

.alert-dismissible .btn-close {
    position: absolute;
    top: var(--space-3);
    right: var(--space-3);
    padding: var(--space-2);
    background: rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: var(--radius-full);
    color: currentColor;
    opacity: 0.7;
    transition: all var(--transition-base);
}

.alert-dismissible .btn-close:hover {
    opacity: 1;
    background: rgba(0, 0, 0, 0.2);
    transform: scale(1.1);
}

/* === BADGE MODERNIZZATI === */
.badge {
    font-size: var(--text-xs);
    font-weight: var(--font-semibold);
    padding: var(--space-1) var(--space-3);
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    display: inline-flex;
    align-items: center;
    gap: var(--space-1);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
}

.badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left var(--transition-slow);
}

.badge:hover::before {
    left: 100%;
}

.badge-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.badge-secondary {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.badge-success {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.badge-danger {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    color: white;
    box-shadow: var(--shadow-sm);
}

.badge-warning {
    background: var(--gradient-accent);
    color: white;
    box-shadow: var(--shadow-sm);
}

.badge-info {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.badge-light {
    background: var(--neutral-100);
    color: var(--neutral-700);
    border: 1px solid var(--neutral-200);
}

.badge-dark {
    background: var(--gradient-dark);
    color: white;
    box-shadow: var(--shadow-sm);
}

/* === STATUS INDICATORS MODERNIZZATI === */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: var(--radius-full);
    display: inline-block;
    margin-right: var(--space-2);
    position: relative;
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: var(--radius-full);
    background: inherit;
    opacity: 0.3;
    animation: statusPulse 2s infinite;
}

@keyframes statusPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.1;
    }
}

.status-active {
    background: var(--success-color);
}

.status-pending {
    background: var(--warning-color);
}

.status-completed {
    background: var(--primary-500);
}

.status-cancelled {
    background: var(--danger-color);
}

.status-draft {
    background: var(--neutral-400);
}

.status-review {
    background: var(--info-color);
}

/* === STATUS BADGE COMBINATIONS === */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-full);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    background: var(--neutral-100);
    border: 1px solid var(--neutral-200);
    transition: all var(--transition-base);
}

.status-badge:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-sm);
}

.status-badge .status-indicator {
    margin-right: 0;
}

.status-badge.status-active {
    background: var(--secondary-50);
    border-color: var(--secondary-200);
    color: var(--secondary-700);
}

.status-badge.status-pending {
    background: var(--accent-50);
    border-color: var(--accent-200);
    color: var(--accent-700);
}

.status-badge.status-completed {
    background: var(--primary-50);
    border-color: var(--primary-200);
    color: var(--primary-700);
}

.status-badge.status-cancelled {
    background: #FEF2F2;
    border-color: #FECACA;
    color: #991B1B;
}

/* === MODALI GLASSMORPHISM === */
.modal-content {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    overflow: hidden;
}

.modal-header {
    background: var(--gradient-neutral);
    border-bottom: 1px solid var(--glass-border);
    padding: var(--space-6) var(--space-8);
    position: relative;
}

.modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.modal-title {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-3);
}

.modal-title i {
    color: var(--primary-500);
    padding: var(--space-2);
    background: var(--primary-50);
    border-radius: var(--radius-lg);
}

.modal-body {
    padding: var(--space-8);
    background: var(--surface-color);
}

.modal-footer {
    background: var(--gradient-neutral);
    border-top: 1px solid var(--glass-border);
    padding: var(--space-6) var(--space-8);
    display: flex;
    gap: var(--space-3);
    justify-content: flex-end;
}

.btn-close {
    background: var(--neutral-100);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-full);
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-base);
}

.btn-close:hover {
    background: var(--danger-color);
    border-color: var(--danger-color);
    color: white;
    transform: scale(1.1);
}

/* === TOOLTIP MODERNIZZATI === */
.tooltip {
    font-size: var(--text-xs);
}

.tooltip-inner {
    background: var(--neutral-800);
    color: white;
    border-radius: var(--radius-lg);
    padding: var(--space-2) var(--space-3);
    font-weight: var(--font-medium);
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
}

/* === PROGRESS BARS === */
.progress {
    height: 8px;
    background: var(--neutral-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    background: var(--gradient-primary);
    transition: width var(--transition-slow);
    position: relative;
    overflow: hidden;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(45deg,
        rgba(255,255,255,0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255,255,255,0.2) 50%,
        rgba(255,255,255,0.2) 75%,
        transparent 75%,
        transparent);
    background-size: 20px 20px;
    animation: progressStripes 1s linear infinite;
}

@keyframes progressStripes {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 20px 0;
    }
}

/* === LOADING STATES === */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    border-radius: inherit;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--neutral-200);
    border-top: 3px solid var(--primary-500);
    border-radius: var(--radius-full);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-dots {
    display: flex;
    gap: var(--space-1);
}

.loading-dots span {
    width: 8px;
    height: 8px;
    background: var(--primary-500);
    border-radius: var(--radius-full);
    animation: loadingDots 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDots {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* === RESPONSIVE UTILITIES === */
@media (max-width: 768px) {
    .management-card {
        padding: var(--space-6);
        margin-bottom: var(--space-4);
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-4);
        padding: var(--space-4) var(--space-6);
    }

    .page-title {
        font-size: var(--text-xl);
    }

    .page-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .form-card {
        padding: var(--space-6);
    }

    .form-section {
        margin-bottom: var(--space-6);
        padding-bottom: var(--space-4);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: var(--space-4) var(--space-6);
    }

    .modal-title {
        font-size: var(--text-lg);
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: var(--radius-lg);
        border-right: none;
        border-bottom: 1px solid var(--neutral-300);
    }

    .btn-group .btn:last-child {
        border-bottom: none;
    }
}

/* === TEMA SCURO === */
[data-theme="dark"] {
    .management-card {
        background: var(--neutral-800);
        border-color: var(--neutral-600);
    }

    .page-header {
        background: var(--glass-bg);
        border-color: var(--glass-border);
    }

    .form-card {
        background: var(--neutral-800);
        border-color: var(--neutral-600);
    }

    .table-card {
        background: var(--neutral-800);
        border-color: var(--neutral-600);
    }

    .table thead th {
        background: var(--neutral-700);
        color: var(--neutral-200);
    }

    .table tbody td {
        color: var(--neutral-300);
    }

    .table tbody tr:hover {
        background: var(--neutral-700);
    }

    .modal-content {
        background: var(--glass-bg);
        border-color: var(--glass-border);
    }

    .modal-header,
    .modal-footer {
        background: var(--neutral-800);
        border-color: var(--neutral-600);
    }

    .modal-body {
        background: var(--neutral-800);
    }

    .loading-overlay {
        background: rgba(31, 41, 55, 0.9);
    }
}

/* === PERFORMANCE OPTIMIZATIONS === */
.management-card,
.table-card,
.form-card,
.btn,
.alert,
.badge {
    will-change: transform, box-shadow;
    transform: translateZ(0); /* Forza accelerazione hardware */
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
    .management-card,
    .table-card,
    .form-card,
    .btn,
    .alert,
    .badge,
    .status-indicator,
    .progress-bar,
    .loading-spinner {
        transition: none !important;
        animation: none !important;
    }
}

/* === PRINT STYLES === */
@media print {
    .management-card,
    .table-card,
    .form-card {
        box-shadow: none !important;
        border: 1px solid #000 !important;
        background: white !important;
    }

    .btn,
    .page-actions {
        display: none !important;
    }

    .alert {
        border: 1px solid #000 !important;
        background: white !important;
        color: #000 !important;
    }
}
