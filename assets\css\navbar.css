/* === NAVBAR GLASSMORPHISM MODERNO - STUDIO TECNICO === */
.navbar {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop); /* Safari support */
    border-bottom: 1px solid var(--glass-border);
    box-shadow: var(--shadow-lg);
    padding: var(--space-4) 0;
    position: sticky;
    top: 0;
    z-index: 1030;
    transition: all var(--transition-base);
}

/* === EFFETTO SCROLL === */
.navbar.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(25px);
    box-shadow: var(--shadow-xl);
    border-bottom-color: var(--neutral-300);
}

/* === BRAND MODERNIZZATO === */
.navbar-brand {
    font-weight: var(--font-bold);
    font-size: var(--text-xl);
    color: var(--primary-600) !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    transition: all var(--transition-base);
    position: relative;
}

.navbar-brand::before {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width var(--transition-base);
}

.navbar-brand:hover::before {
    width: 100%;
}

.navbar-brand:hover {
    color: var(--primary-700) !important;
    transform: translateY(-1px);
}

.navbar-brand img {
    height: 44px;
    width: auto;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
}

.navbar-brand:hover img {
    transform: scale(1.05);
    box-shadow: var(--shadow-md);
}

/* === NAV LINKS MODERNIZZATI === */
.nav-link {
    color: var(--neutral-600) !important;
    font-weight: var(--font-medium);
    font-size: var(--text-base);
    padding: var(--space-3) var(--space-5);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: var(--space-2);
    margin: 0 var(--space-1);
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0.1;
    transition: left var(--transition-slow);
    z-index: -1;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all var(--transition-base);
    transform: translateX(-50%);
}

.nav-link:hover {
    color: var(--primary-600) !important;
    background: var(--primary-50);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.nav-link:hover::before {
    left: 100%;
}

.nav-link:hover::after {
    width: 80%;
}

.nav-link.active {
    color: var(--primary-700) !important;
    background: var(--primary-100);
    box-shadow: var(--shadow-sm);
}

.nav-link.active::after {
    width: 100%;
}

.nav-link i {
    width: 20px;
    text-align: center;
    font-size: var(--text-lg);
    transition: all var(--transition-base);
}

.nav-link:hover i {
    transform: scale(1.1);
}

/* === NAVBAR TOGGLER MODERNIZZATO === */
.navbar-toggler {
    border: 2px solid var(--neutral-300);
    border-radius: var(--radius-lg);
    padding: var(--space-2);
    background: var(--surface-color);
    transition: all var(--transition-base);
}

.navbar-toggler:hover {
    border-color: var(--primary-400);
    background: var(--primary-50);
    transform: scale(1.05);
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    outline: none;
}

.navbar-toggler-icon {
    background-image: none;
    width: 24px;
    height: 24px;
    position: relative;
}

.navbar-toggler-icon::before,
.navbar-toggler-icon::after,
.navbar-toggler-icon {
    background: var(--neutral-600);
    height: 2px;
    border-radius: 2px;
    transition: all var(--transition-base);
}

.navbar-toggler-icon::before,
.navbar-toggler-icon::after {
    content: '';
    position: absolute;
    left: 0;
    width: 100%;
}

.navbar-toggler-icon::before {
    top: -8px;
}

.navbar-toggler-icon::after {
    top: 8px;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon {
    background: transparent;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon::before {
    transform: rotate(45deg);
    top: 0;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon::after {
    transform: rotate(-45deg);
    top: 0;
}

/* === DROPDOWN GLASSMORPHISM === */
.dropdown-menu {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--space-4);
    margin-top: var(--space-2);
    min-width: 220px;
    animation: dropdownFadeIn 0.3s ease-out;
}

@keyframes dropdownFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.dropdown-item {
    color: var(--neutral-700);
    border-radius: var(--radius-lg);
    padding: var(--space-3) var(--space-4);
    font-weight: var(--font-medium);
    font-size: var(--text-sm);
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    gap: var(--space-3);
    position: relative;
    overflow: hidden;
}

.dropdown-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 3px;
    height: 100%;
    background: var(--gradient-primary);
    transform: scaleY(0);
    transition: transform var(--transition-base);
}

.dropdown-item:hover {
    color: var(--primary-700);
    background: var(--primary-50);
    transform: translateX(4px);
    text-decoration: none;
}

.dropdown-item:hover::before {
    transform: scaleY(1);
}

.dropdown-item i {
    width: 16px;
    text-align: center;
    color: var(--neutral-500);
    transition: color var(--transition-base);
}

.dropdown-item:hover i {
    color: var(--primary-600);
}

.dropdown-divider {
    margin: var(--space-3) 0;
    border: none;
    height: 1px;
    background: var(--gradient-primary);
    opacity: 0.2;
}

/* === THEME TOGGLE MODERNIZZATO === */
.theme-toggle {
    background: var(--surface-color);
    border: 2px solid var(--neutral-200);
    color: var(--neutral-600);
    padding: var(--space-3);
    border-radius: var(--radius-full);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-base);
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.theme-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-base);
    border-radius: inherit;
}

.theme-toggle:hover {
    color: white;
    border-color: var(--primary-400);
    transform: scale(1.1);
    box-shadow: var(--shadow-lg);
}

.theme-toggle:hover::before {
    opacity: 1;
}

.theme-toggle i {
    position: relative;
    z-index: 1;
    font-size: var(--text-lg);
    transition: all var(--transition-base);
}

.theme-toggle:hover i {
    transform: rotate(180deg);
}

/* === USER DROPDOWN === */
.user-dropdown .dropdown-toggle {
    background: var(--surface-color);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-full);
    padding: var(--space-2) var(--space-4);
    color: var(--neutral-700);
    font-weight: var(--font-medium);
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    gap: var(--space-2);
}

.user-dropdown .dropdown-toggle:hover {
    border-color: var(--primary-300);
    background: var(--primary-50);
    color: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.user-dropdown .dropdown-toggle::after {
    margin-left: var(--space-2);
    transition: transform var(--transition-base);
}

.user-dropdown.show .dropdown-toggle::after {
    transform: rotate(180deg);
}

/* === AVATAR === */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: var(--radius-full);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: var(--font-semibold);
    font-size: var(--text-sm);
    box-shadow: var(--shadow-sm);
}

/* === PAGE HEADER MODERNIZZATO === */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-8);
    padding: var(--space-6) 0;
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-radius: var(--radius-xl);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
}

.page-title {
    font-size: var(--text-3xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-3);
    line-height: var(--leading-tight);
    position: relative;
}

.page-title::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 60px;
    height: 2px;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
}

.page-title i {
    color: var(--primary-500);
    font-size: var(--text-2xl);
    padding: var(--space-2);
    background: var(--primary-50);
    border-radius: var(--radius-lg);
    transition: all var(--transition-base);
}

.page-title:hover i {
    transform: scale(1.1) rotate(5deg);
    background: var(--primary-100);
}

.page-subtitle {
    font-size: var(--text-base);
    color: var(--text-secondary);
    font-weight: var(--font-normal);
    margin-top: var(--space-1);
}

.page-actions {
    display: flex;
    gap: var(--space-3);
    align-items: center;
}

.page-actions .btn {
    border-radius: var(--radius-lg);
    font-weight: var(--font-medium);
    transition: all var(--transition-base);
}

.page-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* === BREADCRUMB MODERNIZZATO === */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0 0 var(--space-4) 0;
    font-size: var(--text-sm);
}

.breadcrumb-item {
    color: var(--text-secondary);
    font-weight: var(--font-medium);
}

.breadcrumb-item + .breadcrumb-item::before {
    content: '›';
    color: var(--neutral-400);
    font-weight: var(--font-bold);
    margin: 0 var(--space-2);
}

.breadcrumb-item.active {
    color: var(--primary-600);
    font-weight: var(--font-semibold);
}

.breadcrumb-item a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: color var(--transition-base);
}

.breadcrumb-item a:hover {
    color: var(--primary-600);
}

/* === CONTAINER MODERNIZZATO === */
.container-fluid {
    max-width: 1920px;
    padding-left: var(--space-6);
    padding-right: var(--space-6);
    margin: 0 auto;
}

.container-modern {
    max-width: 1400px;
    padding-left: var(--space-6);
    padding-right: var(--space-6);
    margin: 0 auto;
}

/* === NAVBAR COLLAPSE === */
.navbar-collapse {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border-radius: var(--radius-xl);
    margin-top: var(--space-4);
    padding: var(--space-4);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-lg);
}

.navbar-nav {
    gap: var(--space-2);
}

/* === SEARCH BAR === */
.navbar-search {
    position: relative;
    max-width: 300px;
    margin: 0 var(--space-4);
}

.navbar-search input {
    background: var(--surface-color);
    border: 2px solid var(--neutral-200);
    border-radius: var(--radius-full);
    padding: var(--space-3) var(--space-5) var(--space-3) var(--space-12);
    font-size: var(--text-sm);
    transition: all var(--transition-base);
    width: 100%;
}

.navbar-search input:focus {
    border-color: var(--primary-400);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.navbar-search i {
    position: absolute;
    left: var(--space-4);
    top: 50%;
    transform: translateY(-50%);
    color: var(--neutral-400);
    font-size: var(--text-sm);
}

/* === RESPONSIVE DESIGN MODERNIZZATO === */
@media (max-width: 1200px) {
    .container-fluid {
        padding-left: var(--space-4);
        padding-right: var(--space-4);
    }

    .navbar-search {
        max-width: 250px;
        margin: 0 var(--space-2);
    }
}

@media (max-width: 992px) {
    .navbar {
        padding: var(--space-3) 0;
    }

    .navbar-brand {
        font-size: var(--text-lg);
    }

    .navbar-brand img {
        height: 36px;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-4);
        padding: var(--space-4);
    }

    .page-title {
        font-size: var(--text-2xl);
    }

    .page-actions {
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .navbar-search {
        order: -1;
        max-width: 100%;
        margin: var(--space-2) 0;
    }
}

@media (max-width: 768px) {
    .navbar {
        padding: var(--space-2) 0;
    }

    .navbar-brand {
        font-size: var(--text-base);
    }

    .navbar-brand img {
        height: 32px;
    }

    .container-fluid {
        padding-left: var(--space-3);
        padding-right: var(--space-3);
    }

    .page-header {
        margin-bottom: var(--space-6);
        padding: var(--space-3);
    }

    .page-title {
        font-size: var(--text-xl);
    }

    .page-title i {
        font-size: var(--text-lg);
        padding: var(--space-1);
    }

    .nav-link {
        padding: var(--space-4) var(--space-3);
        margin: var(--space-1) 0;
        border-radius: var(--radius-lg);
    }

    .dropdown-menu {
        position: static !important;
        transform: none !important;
        box-shadow: none;
        border: none;
        background: var(--neutral-50);
        margin-top: var(--space-2);
        border-radius: var(--radius-lg);
    }

    .theme-toggle {
        width: 40px;
        height: 40px;
        padding: var(--space-2);
    }
}

@media (max-width: 576px) {
    .navbar-brand span {
        display: none;
    }

    .page-header {
        padding: var(--space-2);
    }

    .page-title {
        font-size: var(--text-lg);
        gap: var(--space-2);
    }

    .page-actions .btn {
        font-size: var(--text-sm);
        padding: var(--space-2) var(--space-3);
    }

    .container-fluid {
        padding-left: var(--space-2);
        padding-right: var(--space-2);
    }
}

/* === LANDSCAPE MOBILE === */
@media (max-height: 500px) and (orientation: landscape) {
    .navbar {
        padding: var(--space-1) 0;
    }

    .page-header {
        margin-bottom: var(--space-4);
        padding: var(--space-2);
    }

    .page-title {
        font-size: var(--text-lg);
    }
}

/* === TEMA SCURO MODERNIZZATO === */
[data-theme="dark"] {
    .navbar {
        background: var(--glass-bg);
        border-bottom-color: var(--glass-border);
        box-shadow: var(--shadow-xl);
    }

    .navbar.scrolled {
        background: rgba(31, 41, 55, 0.95);
        border-bottom-color: var(--neutral-600);
    }

    .navbar-brand {
        color: var(--primary-400) !important;
    }

    .navbar-brand:hover {
        color: var(--primary-300) !important;
    }

    .nav-link {
        color: var(--neutral-300) !important;
    }

    .nav-link:hover {
        color: var(--primary-400) !important;
        background: var(--neutral-800);
    }

    .nav-link.active {
        color: var(--primary-300) !important;
        background: var(--neutral-700);
    }

    .dropdown-menu {
        background: var(--glass-bg);
        border-color: var(--glass-border);
    }

    .dropdown-item {
        color: var(--neutral-200);
    }

    .dropdown-item:hover {
        color: var(--primary-300);
        background: var(--neutral-800);
    }

    .dropdown-item i {
        color: var(--neutral-400);
    }

    .dropdown-item:hover i {
        color: var(--primary-400);
    }

    .theme-toggle {
        background: var(--neutral-800);
        border-color: var(--neutral-600);
        color: var(--neutral-300);
    }

    .theme-toggle:hover {
        border-color: var(--primary-500);
        color: white;
    }

    .user-dropdown .dropdown-toggle {
        background: var(--neutral-800);
        border-color: var(--neutral-600);
        color: var(--neutral-200);
    }

    .user-dropdown .dropdown-toggle:hover {
        border-color: var(--primary-500);
        background: var(--neutral-700);
        color: var(--primary-300);
    }

    .page-header {
        background: var(--glass-bg);
        border-color: var(--glass-border);
    }

    .page-title {
        color: var(--neutral-100);
    }

    .page-title i {
        color: var(--primary-400);
        background: var(--neutral-800);
    }

    .page-title:hover i {
        background: var(--neutral-700);
    }

    .breadcrumb-item {
        color: var(--neutral-400);
    }

    .breadcrumb-item.active {
        color: var(--primary-400);
    }

    .breadcrumb-item a {
        color: var(--neutral-400);
    }

    .breadcrumb-item a:hover {
        color: var(--primary-400);
    }

    .navbar-search input {
        background: var(--neutral-800);
        border-color: var(--neutral-600);
        color: var(--neutral-200);
    }

    .navbar-search input:focus {
        border-color: var(--primary-500);
        background: var(--neutral-700);
    }

    .navbar-search input::placeholder {
        color: var(--neutral-500);
    }

    .navbar-search i {
        color: var(--neutral-500);
    }

    .navbar-collapse {
        background: var(--glass-bg);
        border-color: var(--glass-border);
    }

    .navbar-toggler {
        border-color: var(--neutral-600);
        background: var(--neutral-800);
    }

    .navbar-toggler:hover {
        border-color: var(--primary-500);
        background: var(--neutral-700);
    }

    .navbar-toggler-icon,
    .navbar-toggler-icon::before,
    .navbar-toggler-icon::after {
        background: var(--neutral-300);
    }
}

/* === EFFETTI SPECIALI === */
@keyframes navbarGlow {
    0%, 100% {
        box-shadow: var(--shadow-lg);
    }
    50% {
        box-shadow: var(--shadow-xl), 0 0 20px rgba(99, 102, 241, 0.1);
    }
}

.navbar:hover {
    animation: navbarGlow 2s ease-in-out infinite;
}

/* === SCROLL BEHAVIOR === */
.navbar-scroll-hidden {
    transform: translateY(-100%);
    transition: transform 0.3s ease-in-out;
}

.navbar-scroll-visible {
    transform: translateY(0);
    transition: transform 0.3s ease-in-out;
}

/* === PERFORMANCE OPTIMIZATIONS === */
.navbar,
.nav-link,
.dropdown-menu,
.theme-toggle {
    will-change: transform, background-color, box-shadow;
    transform: translateZ(0); /* Forza accelerazione hardware */
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
    .navbar,
    .nav-link,
    .dropdown-menu,
    .theme-toggle,
    .navbar-toggler-icon,
    .navbar-toggler-icon::before,
    .navbar-toggler-icon::after {
        transition: none !important;
        animation: none !important;
    }
}

/* === HIGH CONTRAST MODE === */
@media (prefers-contrast: high) {
    .navbar {
        border-bottom: 2px solid var(--primary-600);
    }

    .nav-link {
        border: 1px solid transparent;
    }

    .nav-link:hover,
    .nav-link.active {
        border-color: var(--primary-600);
    }
}
