/**
 * Page Transitions - Stili per Transizioni Fluide
 * Studio Tecnico - Animazioni di Navigazione Avanzate
 */

/* === OVERLAY TRANSIZIONE === */
.page-transition-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.page-transition-overlay.active {
    opacity: 1;
}

/* === LOADER === */
.page-loader {
    text-align: center;
    color: var(--text-primary);
}

.loader-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-6);
    padding: var(--space-8);
    background: var(--surface-color);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-2xl);
    border: 1px solid var(--glass-border);
    min-width: 300px;
}

.loader-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid var(--neutral-200);
    border-top: 4px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    position: relative;
}

.loader-spinner::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes pulse {
    0%, 100% { 
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    50% { 
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0.7;
    }
}

.loader-text {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
    animation: fadeInOut 2s ease-in-out infinite;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

.loader-progress {
    width: 200px;
    height: 4px;
    background: var(--neutral-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.loader-progress-bar {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
}

.loader-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.4),
        transparent
    );
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* === PROGRESS BAR GLOBALE === */
.page-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 3px;
    background: var(--gradient-primary);
    z-index: 10000;
    transition: width 0.3s ease, opacity 0.3s ease;
    opacity: 0;
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
}

.page-progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.6),
        transparent
    );
    animation: progressShimmer 1s infinite;
}

@keyframes progressShimmer {
    0% { transform: translateX(-100px); }
    100% { transform: translateX(100px); }
}

/* === TRANSIZIONI PAGINA === */
.page-transition-fade-out {
    opacity: 0;
    transition: opacity 0.6s ease;
}

.page-transition-fade-in {
    opacity: 1;
    transition: opacity 0.6s ease;
}

.page-transition-slide-out {
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.page-transition-slide-in {
    transform: translateX(0);
    transition: transform 0.6s ease;
}

.page-transition-scale-out {
    transform: scale(0.8);
    opacity: 0;
    transition: transform 0.6s ease, opacity 0.6s ease;
}

.page-transition-scale-in {
    transform: scale(1);
    opacity: 1;
    transition: transform 0.6s ease, opacity 0.6s ease;
}

.page-transition-flip-out {
    transform: rotateY(-90deg);
    opacity: 0;
    transition: transform 0.6s ease, opacity 0.6s ease;
    transform-origin: center;
}

.page-transition-flip-in {
    transform: rotateY(0deg);
    opacity: 1;
    transition: transform 0.6s ease, opacity 0.6s ease;
    transform-origin: center;
}

/* === ANIMAZIONI CARICAMENTO PAGINA === */
.page-enter {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.page-enter-active {
    opacity: 1;
    transform: translateY(0);
}

/* === PRELOADER LINK === */
.link-preloading {
    position: relative;
    overflow: hidden;
}

.link-preloading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(99, 102, 241, 0.1),
        transparent
    );
    animation: linkPreload 1s infinite;
}

@keyframes linkPreload {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* === STATI HOVER MIGLIORATI === */
.transition-link {
    position: relative;
    transition: all 0.3s ease;
}

.transition-link:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.transition-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0.1;
    transition: width 0.3s ease;
    z-index: -1;
}

.transition-link:hover::before {
    width: 100%;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .loader-content {
        min-width: 250px;
        padding: var(--space-6);
        gap: var(--space-4);
    }
    
    .loader-spinner {
        width: 40px;
        height: 40px;
        border-width: 3px;
    }
    
    .loader-spinner::after {
        width: 14px;
        height: 14px;
    }
    
    .loader-text {
        font-size: var(--text-base);
    }
    
    .loader-progress {
        width: 150px;
        height: 3px;
    }
    
    .page-progress-bar {
        height: 2px;
    }
}

@media (max-width: 480px) {
    .loader-content {
        min-width: 200px;
        padding: var(--space-4);
        gap: var(--space-3);
    }
    
    .loader-spinner {
        width: 30px;
        height: 30px;
        border-width: 2px;
    }
    
    .loader-spinner::after {
        width: 10px;
        height: 10px;
    }
    
    .loader-text {
        font-size: var(--text-sm);
    }
    
    .loader-progress {
        width: 120px;
        height: 2px;
    }
}

/* === TEMA SCURO === */
[data-theme="dark"] .page-transition-overlay {
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

[data-theme="dark"] .loader-content {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
}

[data-theme="dark"] .loader-spinner {
    border-color: var(--neutral-600);
    border-top-color: var(--primary-400);
}

[data-theme="dark"] .loader-progress {
    background: var(--neutral-600);
}

[data-theme="dark"] .page-progress-bar {
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.3);
}

/* === PERFORMANCE === */
.page-transition-overlay,
.page-loader,
.loader-spinner,
.page-progress-bar {
    will-change: transform, opacity;
    transform: translateZ(0);
}

/* === ACCESSIBILITÀ === */
@media (prefers-reduced-motion: reduce) {
    .loader-spinner,
    .loader-progress-bar::after,
    .page-progress-bar::after {
        animation: none;
    }
    
    .page-transition-overlay,
    .page-enter,
    .transition-link {
        transition-duration: 0.1s;
    }
}

/* === STATI SPECIALI === */
.page-transition-error .loader-spinner {
    border-top-color: var(--danger-500);
    animation-duration: 0.5s;
}

.page-transition-error .loader-text {
    color: var(--danger-500);
}

.page-transition-error .loader-progress-bar {
    background: var(--gradient-danger);
}

.page-transition-success .loader-spinner {
    border-top-color: var(--success-500);
}

.page-transition-success .loader-text {
    color: var(--success-500);
}

.page-transition-success .loader-progress-bar {
    background: var(--gradient-success);
}

/* === ANIMAZIONI AVANZATE === */
@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.page-bounce-enter {
    animation: bounceIn 0.6s ease-out;
}

@keyframes slideInFromRight {
    0% {
        opacity: 0;
        transform: translateX(100%);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.page-slide-right-enter {
    animation: slideInFromRight 0.6s ease-out;
}

@keyframes slideInFromLeft {
    0% {
        opacity: 0;
        transform: translateX(-100%);
    }
    100% {
        opacity: 1;
        transform: translateX(0);
    }
}

.page-slide-left-enter {
    animation: slideInFromLeft 0.6s ease-out;
}

/* === LOADING STATES === */
.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: var(--space-4);
    padding: var(--space-6);
    border-radius: var(--radius-xl);
    background: var(--surface-color);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--neutral-200);
    z-index: 9999;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(5px);
    border-radius: inherit;
}

/* === SPINNER === */
.spinner-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.spinner-ring {
    width: 40px;
    height: 40px;
    border: 3px solid var(--neutral-200);
    border-top: 3px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-large .spinner-ring {
    width: 60px;
    height: 60px;
    border-width: 4px;
}

.loading-small .spinner-ring {
    width: 24px;
    height: 24px;
    border-width: 2px;
}

.spinner-inner {
    position: absolute;
    width: 20px;
    height: 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    animation: pulse 2s ease-in-out infinite;
}

.loading-large .spinner-inner {
    width: 30px;
    height: 30px;
}

.loading-small .spinner-inner {
    width: 12px;
    height: 12px;
}

/* === SKELETON === */
.skeleton-container {
    width: 100%;
    max-width: 400px;
}

.skeleton-line {
    height: 16px;
    background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
    background-size: 200% 100%;
    border-radius: var(--radius-sm);
    margin-bottom: var(--space-3);
    animation: skeleton-loading 1.5s infinite;
}

.skeleton-title {
    height: 24px;
    width: 60%;
}

.skeleton-text {
    width: 100%;
}

.skeleton-text.short {
    width: 40%;
}

.skeleton-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* === PULSE === */
.pulse-container {
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

.pulse-circle {
    width: 12px;
    height: 12px;
    background: var(--primary-500);
    border-radius: 50%;
    animation: pulse-wave 1.4s ease-in-out infinite both;
}

.pulse-2 { animation-delay: -1.1s; }
.pulse-3 { animation-delay: -0.9s; }

@keyframes pulse-wave {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 1;
    }
    40% {
        transform: scale(1);
        opacity: 0.5;
    }
}

/* === DOTS === */
.dots-container {
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

.dot {
    width: 8px;
    height: 8px;
    background: var(--primary-500);
    border-radius: 50%;
    animation: dot-bounce 1.4s ease-in-out infinite both;
}

.dot-2 { animation-delay: -1.1s; }
.dot-3 { animation-delay: -0.9s; }
.dot-4 { animation-delay: -0.7s; }

@keyframes dot-bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* === BARS === */
.bars-container {
    display: flex;
    gap: var(--space-1);
    align-items: end;
    height: 40px;
}

.bar {
    width: 4px;
    background: var(--primary-500);
    border-radius: var(--radius-sm);
    animation: bar-scale 1.2s ease-in-out infinite;
}

.bar-1 { animation-delay: -1.1s; }
.bar-2 { animation-delay: -1.0s; }
.bar-3 { animation-delay: -0.9s; }
.bar-4 { animation-delay: -0.8s; }
.bar-5 { animation-delay: -0.7s; }

@keyframes bar-scale {
    0%, 40%, 100% {
        height: 10px;
    }
    20% {
        height: 40px;
    }
}

/* === PROGRESS === */
.progress-container {
    width: 200px;
    text-align: center;
}

.progress-track {
    width: 100%;
    height: 6px;
    background: var(--neutral-200);
    border-radius: var(--radius-full);
    overflow: hidden;
    margin-bottom: var(--space-2);
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    width: 0%;
    transition: width 0.3s ease;
    position: relative;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: progress-shimmer 1.5s infinite;
}

@keyframes progress-shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-percentage {
    font-size: var(--text-sm);
    font-weight: var(--font-semibold);
    color: var(--text-secondary);
}

/* === LOADER TEXT === */
.loader-text {
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    color: var(--text-secondary);
    text-align: center;
    margin-top: var(--space-2);
}

.loading-large .loader-text {
    font-size: var(--text-base);
}

.loading-small .loader-text {
    font-size: var(--text-xs);
}

/* === COLOR VARIANTS === */
.loading-secondary .spinner-ring { border-top-color: var(--secondary-500); }
.loading-secondary .spinner-inner { background: var(--gradient-secondary); }
.loading-secondary .pulse-circle,
.loading-secondary .dot,
.loading-secondary .bar { background: var(--secondary-500); }
.loading-secondary .progress-fill { background: var(--gradient-secondary); }

.loading-success .spinner-ring { border-top-color: var(--success-500); }
.loading-success .spinner-inner { background: var(--gradient-success); }
.loading-success .pulse-circle,
.loading-success .dot,
.loading-success .bar { background: var(--success-500); }
.loading-success .progress-fill { background: var(--gradient-success); }

.loading-warning .spinner-ring { border-top-color: var(--warning-500); }
.loading-warning .spinner-inner { background: var(--gradient-warning); }
.loading-warning .pulse-circle,
.loading-warning .dot,
.loading-warning .bar { background: var(--warning-500); }
.loading-warning .progress-fill { background: var(--gradient-warning); }

.loading-danger .spinner-ring { border-top-color: var(--danger-500); }
.loading-danger .spinner-inner { background: var(--gradient-danger); }
.loading-danger .pulse-circle,
.loading-danger .dot,
.loading-danger .bar { background: var(--danger-500); }
.loading-danger .progress-fill { background: var(--gradient-danger); }

/* === SKELETON VARIANTS === */
.skeleton-table {
    width: 100%;
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.skeleton-row {
    display: flex;
    gap: var(--space-3);
    padding: var(--space-3);
    border-bottom: 1px solid var(--neutral-100);
}

.skeleton-header {
    background: var(--neutral-100);
}

.skeleton-cell {
    height: 20px;
    flex: 1;
    background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
    background-size: 200% 100%;
    border-radius: var(--radius-sm);
    animation: skeleton-loading 1.5s infinite;
}

.skeleton-header-cell {
    height: 16px;
    background: linear-gradient(90deg, var(--neutral-300) 25%, var(--neutral-200) 50%, var(--neutral-300) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

.skeleton-card {
    width: 100%;
    max-width: 300px;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--surface-color);
    box-shadow: var(--shadow-sm);
}

.skeleton-card-header {
    height: 120px;
    background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

.skeleton-card-body {
    padding: var(--space-4);
}

.skeleton-card-footer {
    height: 40px;
    background: linear-gradient(90deg, var(--neutral-200) 25%, var(--neutral-100) 50%, var(--neutral-200) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .loading-state {
        padding: var(--space-4);
        gap: var(--space-3);
    }

    .progress-container {
        width: 150px;
    }

    .skeleton-container {
        max-width: 300px;
    }
}

/* === TEMA SCURO === */
[data-theme="dark"] .loading-state {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
}

[data-theme="dark"] .loading-overlay {
    background: rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .spinner-ring {
    border-color: var(--neutral-600);
}

[data-theme="dark"] .skeleton-line,
[data-theme="dark"] .skeleton-avatar,
[data-theme="dark"] .skeleton-cell,
[data-theme="dark"] .skeleton-header-cell,
[data-theme="dark"] .skeleton-card-header,
[data-theme="dark"] .skeleton-card-footer {
    background: linear-gradient(90deg, var(--neutral-700) 25%, var(--neutral-600) 50%, var(--neutral-700) 75%);
    background-size: 200% 100%;
}

[data-theme="dark"] .progress-track {
    background: var(--neutral-600);
}

/* === PERFORMANCE === */
.loading-state,
.spinner-ring,
.skeleton-line,
.pulse-circle,
.dot,
.bar {
    will-change: transform, opacity;
    transform: translateZ(0);
}

/* === ACCESSIBILITÀ === */
@media (prefers-reduced-motion: reduce) {
    .spinner-ring,
    .skeleton-line,
    .pulse-circle,
    .dot,
    .bar,
    .progress-fill::after {
        animation: none;
    }

    .loading-state {
        transition-duration: 0.1s;
    }
}
