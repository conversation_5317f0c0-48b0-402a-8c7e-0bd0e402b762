/* === DESIGN SYSTEM MODERNO - STUDIO TECNICO === */
:root {
    /* === COLORI PRIMARI MODERNI === */
    --primary-50: #EEF2FF;
    --primary-100: #E0E7FF;
    --primary-200: #C7D2FE;
    --primary-300: #A5B4FC;
    --primary-400: #818CF8;
    --primary-500: #6366F1;  /* Primary principale - Indigo moderno */
    --primary-600: #5B21B6;
    --primary-700: #4C1D95;
    --primary-800: #3730A3;
    --primary-900: #312E81;

    /* === COLORI SECONDARI === */
    --secondary-50: #ECFDF5;
    --secondary-100: #D1FAE5;
    --secondary-200: #A7F3D0;
    --secondary-300: #6EE7B7;
    --secondary-400: #34D399;
    --secondary-500: #10B981;  /* Secondary principale - Verde emerald */
    --secondary-600: #059669;
    --secondary-700: #047857;
    --secondary-800: #065F46;
    --secondary-900: #064E3B;

    /* === COLORI ACCENT === */
    --accent-50: #FFFBEB;
    --accent-100: #FEF3C7;
    --accent-200: #FDE68A;
    --accent-300: #FCD34D;
    --accent-400: #FBBF24;
    --accent-500: #F59E0B;   /* Accent principale - Amber */
    --accent-600: #D97706;
    --accent-700: #B45309;
    --accent-800: #92400E;
    --accent-900: #78350F;

    /* === NEUTRAL MODERNI === */
    --neutral-50: #FAFBFC;   /* Background ultra-light */
    --neutral-100: #F4F6F8;  /* Background light */
    --neutral-200: #E5E7EB;  /* Border light */
    --neutral-300: #D1D5DB;  /* Border */
    --neutral-400: #9CA3AF;  /* Text muted */
    --neutral-500: #6B7280;  /* Text secondary */
    --neutral-600: #4B5563;  /* Text primary light */
    --neutral-700: #374151;  /* Text primary */
    --neutral-800: #1F2937;  /* Text primary dark */
    --neutral-900: #111827;  /* Text primary ultra-dark */

    /* === COLORI SISTEMA === */
    --danger-color: #EF4444;
    --success-color: #10B981;
    --warning-color: #F59E0B;
    --info-color: #6366F1;

    /* === GRADIENTS MODERNI === */
    --gradient-primary: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
    --gradient-secondary: linear-gradient(135deg, #10B981 0%, #059669 100%);
    --gradient-accent: linear-gradient(135deg, #F59E0B 0%, #EF4444 100%);
    --gradient-neutral: linear-gradient(135deg, #F9FAFB 0%, #F3F4F6 100%);
    --gradient-dark: linear-gradient(135deg, #1F2937 0%, #111827 100%);

    /* === OMBRE MODERNE === */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* === GLASSMORPHISM === */
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: rgba(255, 255, 255, 0.18);
    --glass-backdrop: blur(20px);

    /* === VARIABILI COMPATIBILITÀ (mantengono nomi esistenti) === */
    --primary-color: var(--primary-500);
    --secondary-color: var(--neutral-500);
    --background-color: var(--neutral-50);
    --surface-color: var(--neutral-100);
    --text-primary: var(--neutral-800);
    --text-secondary: var(--neutral-500);
    --border-color: var(--neutral-200);
    --hover-color: var(--primary-600);

    /* === TIPOGRAFIA MIGLIORATA === */
    --font-family-primary: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
    --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

    /* Font Sizes - Scala Tipografica */
    --text-xs: 0.75rem;      /* 12px */
    --text-sm: 0.875rem;     /* 14px */
    --text-base: 1rem;       /* 16px */
    --text-lg: 1.125rem;     /* 18px */
    --text-xl: 1.25rem;      /* 20px */
    --text-2xl: 1.5rem;      /* 24px */
    --text-3xl: 1.875rem;    /* 30px */
    --text-4xl: 2.25rem;     /* 36px */

    /* Font Weights */
    --font-light: 300;
    --font-normal: 400;
    --font-medium: 500;
    --font-semibold: 600;
    --font-bold: 700;
    --font-extrabold: 800;

    /* Line Heights */
    --leading-tight: 1.25;
    --leading-snug: 1.375;
    --leading-normal: 1.5;
    --leading-relaxed: 1.625;
    --leading-loose: 2;

    /* === SPACING SISTEMATICO === */
    --space-1: 0.25rem;   /* 4px */
    --space-2: 0.5rem;    /* 8px */
    --space-3: 0.75rem;   /* 12px */
    --space-4: 1rem;      /* 16px */
    --space-5: 1.25rem;   /* 20px */
    --space-6: 1.5rem;    /* 24px */
    --space-8: 2rem;      /* 32px */
    --space-10: 2.5rem;   /* 40px */
    --space-12: 3rem;     /* 48px */
    --space-16: 4rem;     /* 64px */

    /* === BORDER RADIUS === */
    --radius-sm: 0.375rem;   /* 6px */
    --radius-md: 0.5rem;     /* 8px */
    --radius-lg: 0.75rem;    /* 12px */
    --radius-xl: 1rem;       /* 16px */
    --radius-2xl: 1.5rem;    /* 24px */
    --radius-full: 9999px;   /* Completamente arrotondato */

    /* === TRANSIZIONI === */
    --transition-fast: 0.15s ease-out;
    --transition-base: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
    --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* === TEMA SCURO MODERNO === */
[data-theme="dark"] {
    /* === NEUTRAL INVERTITI PER TEMA SCURO === */
    --neutral-50: #111827;   /* Background ultra-dark */
    --neutral-100: #1F2937;  /* Background dark */
    --neutral-200: #374151;  /* Border dark */
    --neutral-300: #4B5563;  /* Border light */
    --neutral-400: #6B7280;  /* Text muted */
    --neutral-500: #9CA3AF;  /* Text secondary */
    --neutral-600: #D1D5DB;  /* Text primary light */
    --neutral-700: #E5E7EB;  /* Text primary */
    --neutral-800: #F3F4F6;  /* Text primary light */
    --neutral-900: #FAFBFC;  /* Text primary ultra-light */

    /* === COLORI PRIMARI ADATTATI === */
    --primary-500: #8B5CF6;  /* Viola più chiaro per contrasto */
    --primary-600: #7C3AED;
    --secondary-500: #34D399; /* Verde più chiaro */
    --secondary-600: #10B981;

    /* === GLASSMORPHISM SCURO === */
    --glass-bg: rgba(31, 41, 55, 0.25);
    --glass-border: rgba(75, 85, 99, 0.18);

    /* === GRADIENTS SCURI === */
    --gradient-primary: linear-gradient(135deg, #8B5CF6 0%, #6366F1 100%);
    --gradient-secondary: linear-gradient(135deg, #34D399 0%, #10B981 100%);
    --gradient-neutral: linear-gradient(135deg, #1F2937 0%, #111827 100%);

    /* === OMBRE SCURE === */
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 10px 10px -5px rgba(0, 0, 0, 0.2);

    /* === VARIABILI COMPATIBILITÀ AGGIORNATE === */
    --primary-color: var(--primary-500);
    --secondary-color: var(--neutral-400);
    --background-color: var(--neutral-50);
    --surface-color: var(--neutral-100);
    --text-primary: var(--neutral-900);
    --text-secondary: var(--neutral-500);
    --border-color: var(--neutral-200);
    --hover-color: var(--primary-400);

    /* === COLORI SISTEMA SCURI === */
    --danger-color: #F87171;
    --success-color: #4ADE80;
    --warning-color: #FBBF24;
    --info-color: #60A5FA;
}

/* === RESET E STILI BASE MODERNIZZATI === */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
}

body {
    background: var(--gradient-neutral);
    color: var(--text-primary);
    font-family: var(--font-family-primary);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    line-height: var(--leading-normal);
    transition: all var(--transition-base);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* === TIPOGRAFIA MIGLIORATA === */
h1, h2, h3, h4, h5, h6 {
    font-weight: var(--font-semibold);
    line-height: var(--leading-tight);
    color: var(--text-primary);
    margin-bottom: var(--space-4);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
    margin-bottom: var(--space-4);
    line-height: var(--leading-relaxed);
}

small {
    font-size: var(--text-sm);
    color: var(--text-secondary);
}

/* === FOCUS STATES MIGLIORATI === */
:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

:focus-visible {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
}

/* Layout */
.container {
    max-width: 1400px;
    padding: 0 1rem;
    margin: 0 auto;
}

/* Navbar */
.navbar {
    background-color: var(--surface-color) !important;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
}

.navbar-brand {
    color: var(--primary-color) !important;
    font-weight: 700;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-brand i {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.navbar-toggler {
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
    padding: 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color), 0.25);
}

.navbar-toggler-icon {
    background-image: none !important;
    position: relative;
}

.navbar-toggler-icon::before {
    content: '\f0c9';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-primary);
}

.nav-link {
    color: var(--text-secondary) !important;
    font-weight: 500;
    font-size: 1rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-link i {
    color: inherit;
    font-size: 1rem;
    width: 1.25rem;
    text-align: center;
}

.nav-link:hover, 
.nav-link.active {
    color: var(--primary-color) !important;
    background-color: var(--background-color);
}

.dropdown .btn-link.nav-link {
    padding: 0.5rem;
    font-size: 1.25rem;
}

.dropdown .btn-link.nav-link:hover {
    background: none;
    color: var(--primary-color) !important;
}

.dropdown-menu {
    border-color: var(--border-color);
    box-shadow: var(--shadow-md);
    padding: 0.5rem;
}

.dropdown-item {
    color: var(--text-primary);
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
    border-radius: 0.25rem;
}

.dropdown-item:hover {
    background-color: var(--background-color);
    color: var(--primary-color);
}

/* === CARDS MODERNIZZATE === */
.card {
    background: var(--surface-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
    margin-bottom: var(--space-6);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-200);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    background: var(--gradient-neutral);
    border-bottom: 1px solid var(--border-color);
    padding: var(--space-6);
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
    position: relative;
}

.card-body {
    padding: var(--space-8);
    background: var(--surface-color);
}

.card-title {
    color: var(--text-primary);
    font-weight: var(--font-semibold);
    font-size: var(--text-xl);
    margin-bottom: var(--space-4);
    line-height: var(--leading-tight);
}

.card-text {
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
    margin-bottom: var(--space-4);
}

/* === CARD VARIANTS === */
.card-modern {
    background: var(--surface-color);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
    overflow: hidden;
    position: relative;
}

.card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.card-modern:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-300);
}

.card-modern:hover::before {
    opacity: 1;
}

.card-glassmorphism {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-lg);
}

/* === BOTTONI MODERNIZZATI === */
.btn {
    padding: var(--space-3) var(--space-6);
    border-radius: var(--radius-lg);
    font-weight: var(--font-medium);
    font-size: var(--text-base);
    transition: all var(--transition-base);
    border: none;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

/* === BUTTON VARIANTS === */
.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-500);
    border: 2px solid var(--primary-500);
    box-shadow: none;
}

.btn-outline-primary:hover {
    background: var(--primary-500);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.btn-outline-secondary {
    background: transparent;
    color: var(--secondary-500);
    border: 2px solid var(--secondary-500);
}

.btn-outline-secondary:hover {
    background: var(--secondary-500);
    color: white;
    transform: translateY(-1px);
}

/* === BUTTON SIZES === */
.btn-sm {
    padding: var(--space-2) var(--space-4);
    font-size: var(--text-sm);
    border-radius: var(--radius-md);
}

.btn-lg {
    padding: var(--space-4) var(--space-8);
    font-size: var(--text-lg);
    border-radius: var(--radius-xl);
}

/* === BUTTON STATES === */
.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled:hover {
    transform: none !important;
    box-shadow: var(--shadow-sm) !important;
}

/* === BUTTON GROUPS === */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-radius: var(--radius-lg) 0 0 var(--radius-lg);
}

.btn-group .btn:last-child {
    border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
}

/* === FORM MODERNIZZATI === */
.form-control {
    background: var(--surface-color);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-5);
    color: var(--text-primary);
    font-size: var(--text-base);
    font-weight: var(--font-normal);
    transition: all var(--transition-base);
    width: 100%;
}

.form-control:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
    background: var(--neutral-50);
}

.form-control:hover {
    border-color: var(--primary-300);
}

.form-control::placeholder {
    color: var(--neutral-400);
    font-weight: var(--font-normal);
}

.form-label {
    color: var(--text-primary);
    font-weight: var(--font-medium);
    font-size: var(--text-sm);
    margin-bottom: var(--space-2);
    display: block;
}

.form-text {
    color: var(--text-secondary);
    font-size: var(--text-sm);
    margin-top: var(--space-1);
}

/* === FORM GROUPS === */
.form-group {
    margin-bottom: var(--space-6);
}

.form-floating {
    position: relative;
}

.form-floating .form-control {
    padding: var(--space-6) var(--space-5) var(--space-3);
}

.form-floating label {
    position: absolute;
    top: 0;
    left: var(--space-5);
    height: 100%;
    padding: var(--space-6) 0 0;
    pointer-events: none;
    border: none;
    transform-origin: 0 0;
    transition: all var(--transition-base);
    color: var(--neutral-400);
}

.form-floating .form-control:focus ~ label,
.form-floating .form-control:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* === SELECT MODERNIZZATO === */
.form-select {
    background: var(--surface-color) url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e") no-repeat right var(--space-3) center/16px 12px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--space-4) var(--space-10) var(--space-4) var(--space-5);
    color: var(--text-primary);
    transition: all var(--transition-base);
}

.form-select:focus {
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

/* === CHECKBOX E RADIO MODERNIZZATI === */
.form-check-input {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--surface-color);
    transition: all var(--transition-base);
}

.form-check-input:checked {
    background: var(--gradient-primary);
    border-color: var(--primary-500);
}

.form-check-input:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
    outline: none;
}

.form-check-label {
    color: var(--text-primary);
    font-weight: var(--font-medium);
    margin-left: var(--space-2);
}

/* Tabelle */
.table {
    background-color: var(--surface-color);
    color: var(--text-primary);
    border-radius: 0.5rem;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.table th {
    background-color: var(--background-color);
    color: var(--text-secondary);
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.05em;
    padding: 1rem;
}

.table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table-hover tbody tr:hover {
    background-color: var(--background-color);
}

/* Alert */
.alert {
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    border: none;
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
}

.alert-success {
    background-color: rgba(34, 197, 94, 0.1);
    color: var(--success-color);
}

/* Badge */
.badge {
    padding: 0.35em 0.65em;
    font-weight: 500;
    border-radius: 9999px;
}

.badge.bg-primary { background-color: var(--primary-color) !important; }
.badge.bg-success { background-color: var(--success-color) !important; }
.badge.bg-danger { background-color: var(--danger-color) !important; }
.badge.bg-warning { background-color: var(--warning-color) !important; }
.badge.bg-info { background-color: var(--info-color) !important; }

/* Utilities */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--text-secondary) !important; }
.text-success { color: var(--success-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-info { background-color: var(--info-color) !important; }

/* Animazioni */
.fade-enter {
    opacity: 0;
    transform: translateY(10px);
}

.fade-enter-active {
    opacity: 1;
    transform: translateY(0);
    transition: opacity 0.3s, transform 0.3s;
}

/* Media Queries */
@media (max-width: 768px) {
    .navbar {
        padding: 0.5rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .table-responsive {
        border-radius: 0.5rem;
        box-shadow: var(--shadow-sm);
    }
    
    .container {
        padding: 0 0.5rem;
    }
}