/**
 * Advanced Tables - Sistema DataTables Modernizzato
 * Studio Tecnico - Tabelle con Filtri Avanzati e Export
 */

class AdvancedTables {
    constructor() {
        this.tables = new Map();
        this.defaultConfig = this.getDefaultConfig();
        this.init();
    }
    
    init() {
        // Carica estensioni DataTables necessarie
        this.loadExtensions();
        
        // Inizializza tutte le tabelle
        this.initializeTables();
        
        // Setup eventi globali
        this.setupGlobalEvents();
        
        console.log('📊 Advanced Tables inizializzato');
    }
    
    // === CONFIGURAZIONE DEFAULT === //
    getDefaultConfig() {
        return {
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/it-IT.json'
            },
            responsive: true,
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Tutti"]],
            dom: '<"table-controls"<"row"<"col-sm-6"<"table-filters">><"col-sm-6"<"table-actions">>>>rtip',
            buttons: [
                {
                    extend: 'collection',
                    text: '<i class="fas fa-download me-2"></i>Export',
                    className: 'btn btn-outline-primary btn-sm',
                    buttons: [
                        {
                            extend: 'excel',
                            text: '<i class="fas fa-file-excel me-2"></i>Excel',
                            className: 'btn btn-success btn-sm',
                            title: 'Studio Tecnico - Export',
                            exportOptions: {
                                columns: ':visible:not(.no-export)'
                            }
                        },
                        {
                            extend: 'pdf',
                            text: '<i class="fas fa-file-pdf me-2"></i>PDF',
                            className: 'btn btn-danger btn-sm',
                            title: 'Studio Tecnico - Export',
                            orientation: 'landscape',
                            pageSize: 'A4',
                            exportOptions: {
                                columns: ':visible:not(.no-export)'
                            }
                        },
                        {
                            extend: 'csv',
                            text: '<i class="fas fa-file-csv me-2"></i>CSV',
                            className: 'btn btn-info btn-sm',
                            title: 'Studio Tecnico - Export',
                            exportOptions: {
                                columns: ':visible:not(.no-export)'
                            }
                        },
                        {
                            extend: 'print',
                            text: '<i class="fas fa-print me-2"></i>Stampa',
                            className: 'btn btn-secondary btn-sm',
                            title: 'Studio Tecnico',
                            exportOptions: {
                                columns: ':visible:not(.no-export)'
                            }
                        }
                    ]
                },
                {
                    extend: 'colvis',
                    text: '<i class="fas fa-columns me-2"></i>Colonne',
                    className: 'btn btn-outline-secondary btn-sm'
                }
            ],
            columnDefs: [
                {
                    targets: 'no-sort',
                    orderable: false
                },
                {
                    targets: 'text-center',
                    className: 'text-center'
                },
                {
                    targets: 'text-end',
                    className: 'text-end'
                }
            ],
            drawCallback: function(settings) {
                // Riapplica stili dopo il ridisegno
                this.api().rows().nodes().to$().each(function() {
                    $(this).addClass('table-row-modern');
                });
                
                // Reinizializza tooltip
                $('[data-bs-toggle="tooltip"]').tooltip();
            },
            initComplete: function(settings, json) {
                // Aggiungi filtri personalizzati
                const api = this.api();
                const tableId = $(api.table().node()).attr('id');
                window.advancedTables.addCustomFilters(tableId, api);
            }
        };
    }
    
    // === CARICAMENTO ESTENSIONI === //
    loadExtensions() {
        // Verifica se le estensioni sono già caricate
        if (typeof $.fn.DataTable.Buttons === 'undefined') {
            // Carica Buttons extension
            const buttonsCSS = document.createElement('link');
            buttonsCSS.rel = 'stylesheet';
            buttonsCSS.href = 'https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap5.min.css';
            document.head.appendChild(buttonsCSS);
            
            const buttonsJS = document.createElement('script');
            buttonsJS.src = 'https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js';
            document.head.appendChild(buttonsJS);
            
            // JSZip per Excel export
            const jszipJS = document.createElement('script');
            jszipJS.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
            document.head.appendChild(jszipJS);
            
            // PDFMake per PDF export
            const pdfmakeJS = document.createElement('script');
            pdfmakeJS.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.2.7/pdfmake.min.js';
            document.head.appendChild(pdfmakeJS);
        }
    }
    
    // === INIZIALIZZAZIONE TABELLE === //
    initializeTables() {
        // Trova tutte le tabelle con classe 'table'
        $('.table').each((index, element) => {
            const $table = $(element);
            const tableId = $table.attr('id') || `table-${index}`;
            $table.attr('id', tableId);
            
            // Configurazione specifica per tipo di tabella
            const config = this.getTableConfig(tableId, $table);
            
            // Inizializza DataTable
            const dataTable = $table.DataTable(config);
            
            // Salva riferimento
            this.tables.set(tableId, dataTable);
            
            // Aggiungi classe moderna
            $table.addClass('table-modern');
        });
    }
    
    // === CONFIGURAZIONE SPECIFICA PER TABELLA === //
    getTableConfig(tableId, $table) {
        const config = { ...this.defaultConfig };
        
        // Configurazioni specifiche basate sul contesto
        if (tableId.includes('clienti') || $table.closest('.clienti-table').length) {
            config.order = [[1, 'asc']]; // Ordina per nome
            config.columnDefs.push({
                targets: [0], // Colonna tipo
                render: function(data, type, row) {
                    if (type === 'display') {
                        const badgeClass = data === 'privato' ? 'badge-primary' : 'badge-success';
                        return `<span class="badge ${badgeClass}">${data.charAt(0).toUpperCase() + data.slice(1)}</span>`;
                    }
                    return data;
                }
            });
        }
        
        if (tableId.includes('progetti') || $table.closest('.progetti-table').length) {
            config.order = [[3, 'desc']]; // Ordina per data inizio
            config.columnDefs.push({
                targets: [5], // Colonna budget
                render: function(data, type, row) {
                    if (type === 'display' && data) {
                        return '€ ' + parseFloat(data).toLocaleString('it-IT', {minimumFractionDigits: 2});
                    }
                    return data;
                }
            });
        }
        
        if (tableId.includes('pratiche') || $table.closest('.pratiche-table').length) {
            config.order = [[5, 'desc']]; // Ordina per data apertura
            config.columnDefs.push({
                targets: [4], // Colonna stato
                render: function(data, type, row) {
                    if (type === 'display') {
                        const statusMap = {
                            'in_corso': 'warning',
                            'completata': 'success',
                            'sospesa': 'secondary',
                            'annullata': 'danger'
                        };
                        const badgeClass = statusMap[data] || 'secondary';
                        const displayText = data.replace('_', ' ').charAt(0).toUpperCase() + data.slice(1).replace('_', ' ');
                        return `<div class="d-flex align-items-center">
                                    <span class="status-indicator status-${data}"></span>
                                    <span class="badge badge-${badgeClass}">${displayText}</span>
                                </div>`;
                    }
                    return data;
                }
            });
        }
        
        return config;
    }
    
    // === FILTRI PERSONALIZZATI === //
    addCustomFilters(tableId, api) {
        const $table = $(`#${tableId}`);
        const $filtersContainer = $table.closest('.dataTables_wrapper').find('.table-filters');
        
        if ($filtersContainer.length === 0) return;
        
        // Filtro per stato (se presente)
        if ($table.find('thead th:contains("Stato")').length) {
            this.addStatusFilter($filtersContainer, api);
        }
        
        // Filtro per tipo cliente (se presente)
        if ($table.find('thead th:contains("Tipo")').length) {
            this.addTypeFilter($filtersContainer, api);
        }
        
        // Filtro per data (se presente)
        if ($table.find('thead th:contains("Data")').length) {
            this.addDateFilter($filtersContainer, api);
        }
        
        // Ricerca avanzata
        this.addAdvancedSearch($filtersContainer, api);
    }
    
    addStatusFilter($container, api) {
        const $filter = $(`
            <div class="filter-group">
                <label class="filter-label">Stato:</label>
                <select class="form-select form-select-sm status-filter">
                    <option value="">Tutti</option>
                    <option value="in_corso">In Corso</option>
                    <option value="completata">Completata</option>
                    <option value="sospesa">Sospesa</option>
                    <option value="annullata">Annullata</option>
                </select>
            </div>
        `);
        
        $container.append($filter);
        
        $filter.find('.status-filter').on('change', function() {
            const value = $(this).val();
            api.column(4).search(value).draw(); // Assumendo che stato sia colonna 4
        });
    }
    
    addTypeFilter($container, api) {
        const $filter = $(`
            <div class="filter-group">
                <label class="filter-label">Tipo:</label>
                <select class="form-select form-select-sm type-filter">
                    <option value="">Tutti</option>
                    <option value="privato">Privato</option>
                    <option value="azienda">Azienda</option>
                </select>
            </div>
        `);
        
        $container.append($filter);
        
        $filter.find('.type-filter').on('change', function() {
            const value = $(this).val();
            api.column(0).search(value).draw(); // Assumendo che tipo sia colonna 0
        });
    }
    
    addDateFilter($container, api) {
        const $filter = $(`
            <div class="filter-group">
                <label class="filter-label">Periodo:</label>
                <div class="date-range-filter">
                    <input type="date" class="form-control form-control-sm date-from" placeholder="Da">
                    <span class="date-separator">-</span>
                    <input type="date" class="form-control form-control-sm date-to" placeholder="A">
                </div>
            </div>
        `);
        
        $container.append($filter);
        
        const $dateFrom = $filter.find('.date-from');
        const $dateTo = $filter.find('.date-to');
        
        function filterByDate() {
            const dateFrom = $dateFrom.val();
            const dateTo = $dateTo.val();
            
            $.fn.dataTable.ext.search.push(function(settings, data, dataIndex) {
                const dateCol = data[5] || data[3]; // Prova colonna 5 o 3 per la data
                if (!dateCol || dateCol === '-') return true;
                
                const rowDate = new Date(dateCol.split('/').reverse().join('-'));
                const minDate = dateFrom ? new Date(dateFrom) : null;
                const maxDate = dateTo ? new Date(dateTo) : null;
                
                if (minDate && rowDate < minDate) return false;
                if (maxDate && rowDate > maxDate) return false;
                
                return true;
            });
            
            api.draw();
            
            // Rimuovi il filtro dopo l'uso
            $.fn.dataTable.ext.search.pop();
        }
        
        $dateFrom.on('change', filterByDate);
        $dateTo.on('change', filterByDate);
    }
    
    addAdvancedSearch($container, api) {
        const $search = $(`
            <div class="filter-group advanced-search">
                <label class="filter-label">Ricerca:</label>
                <div class="search-input-group">
                    <input type="text" class="form-control form-control-sm advanced-search-input" placeholder="Ricerca avanzata...">
                    <button class="btn btn-outline-secondary btn-sm search-clear" type="button">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `);
        
        $container.append($search);
        
        const $input = $search.find('.advanced-search-input');
        const $clear = $search.find('.search-clear');
        
        $input.on('keyup', function() {
            api.search($(this).val()).draw();
        });
        
        $clear.on('click', function() {
            $input.val('');
            api.search('').draw();
        });
    }
    
    // === EVENTI GLOBALI === //
    setupGlobalEvents() {
        // Refresh tabelle
        $(document).on('click', '.table-refresh', (e) => {
            const tableId = $(e.target).closest('.dataTables_wrapper').find('table').attr('id');
            if (this.tables.has(tableId)) {
                this.tables.get(tableId).ajax.reload();
            }
        });
        
        // Toggle filtri
        $(document).on('click', '.toggle-filters', (e) => {
            $(e.target).closest('.dataTables_wrapper').find('.table-filters').slideToggle();
        });
    }
    
    // === UTILITY METHODS === //
    getTable(tableId) {
        return this.tables.get(tableId);
    }
    
    refreshTable(tableId) {
        if (this.tables.has(tableId)) {
            this.tables.get(tableId).ajax.reload();
        }
    }
    
    exportTable(tableId, format) {
        if (this.tables.has(tableId)) {
            const table = this.tables.get(tableId);
            table.button(`.buttons-${format}`).trigger();
        }
    }
    
    // === DESTROY === //
    destroy() {
        this.tables.forEach(table => {
            table.destroy();
        });
        this.tables.clear();
    }
}

// === EXPORT PER USO GLOBALE === //
window.AdvancedTables = AdvancedTables;
