/**
 * Breadcrumb Dinamici - Script per Navigazione Intelligente
 * Studio Tecnico - Sistema di Breadcrumb Avanzato
 */

class BreadcrumbManager {
    constructor() {
        this.breadcrumbNav = null;
        this.breadcrumbItems = [];
        this.history = [];
        this.maxHistoryLength = 10;
        
        this.init();
    }
    
    init() {
        this.breadcrumbNav = document.querySelector('.breadcrumb-nav');
        if (!this.breadcrumbNav) return;
        
        this.breadcrumbItems = Array.from(this.breadcrumbNav.querySelectorAll('.breadcrumb-item-modern'));
        
        this.setupEventListeners();
        this.setupKeyboardNavigation();
        this.setupHistoryTracking();
        this.setupResponsiveHandling();
        this.animateItems();
        
        console.log('🧭 Breadcrumb Manager inizializzato');
    }
    
    // === SETUP EVENT LISTENERS === //
    setupEventListeners() {
        // Click tracking per analytics
        this.breadcrumbItems.forEach((item, index) => {
            const link = item.querySelector('a');
            if (link) {
                link.addEventListener('click', (e) => {
                    this.trackBreadcrumbClick(link.textContent.trim(), link.href, index);
                });
            }
        });
        
        // Hover effects avanzati
        this.breadcrumbItems.forEach(item => {
            item.addEventListener('mouseenter', () => {
                this.showTooltip(item);
                this.highlightPath(item);
            });
            
            item.addEventListener('mouseleave', () => {
                this.hideTooltip();
                this.clearHighlight();
            });
        });
        
        // Gestione azioni breadcrumb
        const backBtn = document.querySelector('.breadcrumb-action-btn[title="Indietro"]');
        if (backBtn) {
            backBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.goBack();
            });
        }
        
        const homeBtn = document.querySelector('.breadcrumb-action-btn[title="Dashboard"]');
        if (homeBtn) {
            homeBtn.addEventListener('click', (e) => {
                this.trackBreadcrumbClick('Dashboard', homeBtn.href, -1);
            });
        }
    }
    
    // === KEYBOARD NAVIGATION === //
    setupKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Alt + Left Arrow = Indietro
            if (e.altKey && e.key === 'ArrowLeft') {
                e.preventDefault();
                this.goBack();
            }
            
            // Alt + Right Arrow = Avanti (se disponibile)
            if (e.altKey && e.key === 'ArrowRight') {
                e.preventDefault();
                this.goForward();
            }
            
            // Alt + Home = Dashboard
            if (e.altKey && e.key === 'Home') {
                e.preventDefault();
                window.location.href = BASE_URL + 'dashboard';
            }
            
            // Ctrl + P = Stampa
            if (e.ctrlKey && e.key === 'p') {
                // Lascia che il browser gestisca la stampa
                this.trackBreadcrumbClick('Print', window.location.href, -2);
            }
        });
    }
    
    // === HISTORY TRACKING === //
    setupHistoryTracking() {
        // Salva la pagina corrente nella cronologia
        this.addToHistory(window.location.href, document.title);
        
        // Ascolta i cambiamenti di pagina
        window.addEventListener('beforeunload', () => {
            this.saveHistoryToStorage();
        });
        
        // Carica cronologia salvata
        this.loadHistoryFromStorage();
    }
    
    addToHistory(url, title) {
        const entry = {
            url: url,
            title: title,
            timestamp: Date.now()
        };
        
        // Rimuovi duplicati
        this.history = this.history.filter(item => item.url !== url);
        
        // Aggiungi in cima
        this.history.unshift(entry);
        
        // Mantieni solo gli ultimi N elementi
        if (this.history.length > this.maxHistoryLength) {
            this.history = this.history.slice(0, this.maxHistoryLength);
        }
    }
    
    saveHistoryToStorage() {
        try {
            localStorage.setItem('breadcrumb_history', JSON.stringify(this.history));
        } catch (e) {
            console.warn('Impossibile salvare cronologia breadcrumb:', e);
        }
    }
    
    loadHistoryFromStorage() {
        try {
            const saved = localStorage.getItem('breadcrumb_history');
            if (saved) {
                this.history = JSON.parse(saved);
            }
        } catch (e) {
            console.warn('Impossibile caricare cronologia breadcrumb:', e);
            this.history = [];
        }
    }
    
    // === RESPONSIVE HANDLING === //
    setupResponsiveHandling() {
        let resizeTimeout;
        
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.handleResponsiveLayout();
            }, 250);
        });
        
        // Esegui al caricamento
        this.handleResponsiveLayout();
    }
    
    handleResponsiveLayout() {
        const width = window.innerWidth;
        
        if (width <= 576) {
            this.enableMobileLayout();
        } else if (width <= 768) {
            this.enableTabletLayout();
        } else {
            this.enableDesktopLayout();
        }
    }
    
    enableMobileLayout() {
        this.breadcrumbNav?.classList.add('breadcrumb-mobile');
        this.breadcrumbNav?.classList.remove('breadcrumb-tablet', 'breadcrumb-desktop');
        
        // Mostra solo gli ultimi 2 elementi
        this.breadcrumbItems.forEach((item, index) => {
            if (index < this.breadcrumbItems.length - 2) {
                item.style.display = 'none';
            } else {
                item.style.display = 'flex';
            }
        });
    }
    
    enableTabletLayout() {
        this.breadcrumbNav?.classList.add('breadcrumb-tablet');
        this.breadcrumbNav?.classList.remove('breadcrumb-mobile', 'breadcrumb-desktop');
        
        // Mostra tutti gli elementi ma con testo ridotto
        this.breadcrumbItems.forEach(item => {
            item.style.display = 'flex';
        });
    }
    
    enableDesktopLayout() {
        this.breadcrumbNav?.classList.add('breadcrumb-desktop');
        this.breadcrumbNav?.classList.remove('breadcrumb-mobile', 'breadcrumb-tablet');
        
        // Mostra tutti gli elementi
        this.breadcrumbItems.forEach(item => {
            item.style.display = 'flex';
        });
    }
    
    // === ANIMAZIONI === //
    animateItems() {
        this.breadcrumbItems.forEach((item, index) => {
            item.style.setProperty('--item-index', index);
            
            // Animazione di entrata
            setTimeout(() => {
                item.style.opacity = '1';
                item.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }
    
    // === TOOLTIP === //
    showTooltip(item) {
        const link = item.querySelector('a');
        const span = item.querySelector('span');
        const text = link ? link.textContent.trim() : span?.textContent.trim();
        
        if (!text) return;
        
        // Crea tooltip se non esiste
        let tooltip = document.getElementById('breadcrumb-tooltip');
        if (!tooltip) {
            tooltip = document.createElement('div');
            tooltip.id = 'breadcrumb-tooltip';
            tooltip.className = 'breadcrumb-tooltip';
            document.body.appendChild(tooltip);
        }
        
        tooltip.textContent = text;
        tooltip.style.display = 'block';
        
        // Posiziona tooltip
        const rect = item.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.bottom + 8 + 'px';
    }
    
    hideTooltip() {
        const tooltip = document.getElementById('breadcrumb-tooltip');
        if (tooltip) {
            tooltip.style.display = 'none';
        }
    }
    
    // === HIGHLIGHT PATH === //
    highlightPath(targetItem) {
        const targetIndex = this.breadcrumbItems.indexOf(targetItem);
        
        this.breadcrumbItems.forEach((item, index) => {
            if (index <= targetIndex) {
                item.classList.add('breadcrumb-highlighted');
            }
        });
    }
    
    clearHighlight() {
        this.breadcrumbItems.forEach(item => {
            item.classList.remove('breadcrumb-highlighted');
        });
    }
    
    // === NAVIGATION === //
    goBack() {
        if (this.history.length > 1) {
            // Vai alla pagina precedente nella cronologia
            const previousPage = this.history[1];
            window.location.href = previousPage.url;
        } else {
            // Fallback al browser history
            window.history.back();
        }
    }
    
    goForward() {
        window.history.forward();
    }
    
    // === ANALYTICS === //
    trackBreadcrumbClick(text, url, index) {
        const event = {
            type: 'breadcrumb_click',
            text: text,
            url: url,
            index: index,
            timestamp: Date.now(),
            page: window.location.href
        };
        
        // Invia a analytics (se disponibile)
        if (typeof gtag !== 'undefined') {
            gtag('event', 'breadcrumb_click', {
                'breadcrumb_text': text,
                'breadcrumb_index': index
            });
        }
        
        // Log per debug
        console.log('📊 Breadcrumb click tracked:', event);
    }
    
    // === UTILITY === //
    refresh() {
        // Ricarica i breadcrumb (utile per SPA)
        window.location.reload();
    }
    
    updateBreadcrumb(newBreadcrumbs) {
        // Aggiorna dinamicamente i breadcrumb (per future implementazioni AJAX)
        console.log('🔄 Aggiornamento breadcrumb:', newBreadcrumbs);
    }
}

// === CSS DINAMICO PER TOOLTIP === //
const breadcrumbCSS = `
.breadcrumb-tooltip {
    position: absolute;
    background: var(--neutral-800);
    color: white;
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    font-size: var(--text-xs);
    font-weight: var(--font-medium);
    z-index: 1000;
    pointer-events: none;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(10px);
    display: none;
    animation: tooltipFadeIn 0.2s ease-out;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.breadcrumb-highlighted {
    background: var(--primary-50) !important;
    border-radius: var(--radius-lg);
    transform: scale(1.02);
}

[data-theme="dark"] .breadcrumb-tooltip {
    background: var(--neutral-700);
    border: 1px solid var(--neutral-600);
}

[data-theme="dark"] .breadcrumb-highlighted {
    background: var(--neutral-800) !important;
}
`;

// Aggiungi CSS dinamico
const style = document.createElement('style');
style.textContent = breadcrumbCSS;
document.head.appendChild(style);

// === INIZIALIZZAZIONE === //
document.addEventListener('DOMContentLoaded', function() {
    window.breadcrumbManager = new BreadcrumbManager();
});

// === EXPORT PER USO GLOBALE === //
window.BreadcrumbManager = BreadcrumbManager;
