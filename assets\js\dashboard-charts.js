/**
 * Dashboard Charts - Grafici Interattivi con Chart.js
 * Studio Tecnico - Visualizzazione Dati Avanzata
 */

// === CONFIGURAZIONE GLOBALE CHART.JS === //
Chart.defaults.font.family = 'Inter, system-ui, sans-serif';
Chart.defaults.font.size = 12;
Chart.defaults.color = 'rgb(107, 114, 128)'; // --neutral-500
Chart.defaults.borderColor = 'rgb(229, 231, 235)'; // --neutral-200
Chart.defaults.backgroundColor = 'rgba(99, 102, 241, 0.1)'; // --primary-500 con alpha

// === PALETTE COLORI MODERNA === //
const chartColors = {
    primary: 'rgb(99, 102, 241)',      // --primary-500
    secondary: 'rgb(16, 185, 129)',    // --secondary-500
    accent: 'rgb(245, 158, 11)',       // --accent-500
    danger: 'rgb(239, 68, 68)',        // --danger-color
    success: 'rgb(34, 197, 94)',       // --success-color
    warning: 'rgb(251, 191, 36)',      // --warning-color
    info: 'rgb(59, 130, 246)',         // --info-color
    neutral: 'rgb(156, 163, 175)',     // --neutral-400
    
    // Gradients
    primaryGradient: ['rgb(99, 102, 241)', 'rgb(139, 92, 246)'],
    secondaryGradient: ['rgb(16, 185, 129)', 'rgb(5, 150, 105)'],
    accentGradient: ['rgb(245, 158, 11)', 'rgb(239, 68, 68)']
};

// === CONFIGURAZIONI CHART COMUNI === //
const commonOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
        legend: {
            position: 'bottom',
            labels: {
                usePointStyle: true,
                padding: 20,
                font: {
                    size: 11,
                    weight: '500'
                }
            }
        },
        tooltip: {
            backgroundColor: 'rgba(17, 24, 39, 0.95)',
            titleColor: 'white',
            bodyColor: 'white',
            borderColor: 'rgb(99, 102, 241)',
            borderWidth: 1,
            cornerRadius: 12,
            padding: 12,
            displayColors: true,
            titleFont: {
                size: 13,
                weight: '600'
            },
            bodyFont: {
                size: 12,
                weight: '500'
            }
        }
    },
    animation: {
        duration: 1500,
        easing: 'easeInOutCubic'
    },
    interaction: {
        intersect: false,
        mode: 'index'
    }
};

// === CLASSE DASHBOARD CHARTS === //
class DashboardCharts {
    constructor(stats) {
        this.stats = stats;
        this.charts = {};
        this.init();
    }
    
    init() {
        this.createOverviewChart();
        this.createStatusDistributionChart();
        this.createTrendChart();
        this.createPerformanceChart();
        
        // Aggiorna tema quando cambia
        this.observeThemeChanges();
    }
    
    // === GRAFICO PANORAMICA === //
    createOverviewChart() {
        const ctx = document.getElementById('overviewChart');
        if (!ctx) return;
        
        const data = {
            labels: ['Clienti', 'Progetti', 'Pratiche Attive', 'Scadenze'],
            datasets: [{
                label: 'Totali',
                data: [
                    this.stats.total_clients,
                    this.stats.total_projects,
                    this.stats.active_practices,
                    this.stats.pending_deadlines
                ],
                backgroundColor: [
                    chartColors.primary,
                    chartColors.secondary,
                    chartColors.accent,
                    chartColors.danger
                ],
                borderColor: [
                    chartColors.primary,
                    chartColors.secondary,
                    chartColors.accent,
                    chartColors.danger
                ],
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false
            }]
        };
        
        this.charts.overview = new Chart(ctx, {
            type: 'bar',
            data: data,
            options: {
                ...commonOptions,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(229, 231, 235, 0.5)'
                        },
                        ticks: {
                            font: {
                                size: 11
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                size: 11,
                                weight: '500'
                            }
                        }
                    }
                },
                plugins: {
                    ...commonOptions.plugins,
                    title: {
                        display: true,
                        text: 'Panoramica Generale',
                        font: {
                            size: 16,
                            weight: '600'
                        },
                        padding: 20
                    }
                }
            }
        });
    }
    
    // === GRAFICO DISTRIBUZIONE STATI === //
    createStatusDistributionChart() {
        const ctx = document.getElementById('statusChart');
        if (!ctx) return;
        
        // Simula dati di distribuzione stati pratiche
        const statusData = {
            'In Corso': Math.floor(this.stats.active_practices * 0.4),
            'In Revisione': Math.floor(this.stats.active_practices * 0.3),
            'In Attesa': Math.floor(this.stats.active_practices * 0.2),
            'Completate': Math.floor(this.stats.active_practices * 0.1)
        };
        
        const data = {
            labels: Object.keys(statusData),
            datasets: [{
                data: Object.values(statusData),
                backgroundColor: [
                    chartColors.accent,
                    chartColors.warning,
                    chartColors.info,
                    chartColors.success
                ],
                borderColor: 'white',
                borderWidth: 3,
                hoverOffset: 10
            }]
        };
        
        this.charts.status = new Chart(ctx, {
            type: 'doughnut',
            data: data,
            options: {
                ...commonOptions,
                cutout: '60%',
                plugins: {
                    ...commonOptions.plugins,
                    title: {
                        display: true,
                        text: 'Distribuzione Stati Pratiche',
                        font: {
                            size: 16,
                            weight: '600'
                        },
                        padding: 20
                    }
                }
            }
        });
    }
    
    // === GRAFICO TREND TEMPORALE === //
    createTrendChart() {
        const ctx = document.getElementById('trendChart');
        if (!ctx) return;
        
        // Simula dati trend ultimi 6 mesi
        const months = ['Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'];
        const clientiData = [12, 15, 18, 22, 25, this.stats.total_clients];
        const progettiData = [8, 10, 12, 15, 18, this.stats.total_projects];
        
        const data = {
            labels: months,
            datasets: [
                {
                    label: 'Clienti',
                    data: clientiData,
                    borderColor: chartColors.primary,
                    backgroundColor: chartColors.primary + '20',
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: chartColors.primary,
                    pointBorderColor: 'white',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                },
                {
                    label: 'Progetti',
                    data: progettiData,
                    borderColor: chartColors.secondary,
                    backgroundColor: chartColors.secondary + '20',
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: chartColors.secondary,
                    pointBorderColor: 'white',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8
                }
            ]
        };
        
        this.charts.trend = new Chart(ctx, {
            type: 'line',
            data: data,
            options: {
                ...commonOptions,
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(229, 231, 235, 0.5)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                plugins: {
                    ...commonOptions.plugins,
                    title: {
                        display: true,
                        text: 'Trend Crescita (Ultimi 6 Mesi)',
                        font: {
                            size: 16,
                            weight: '600'
                        },
                        padding: 20
                    }
                }
            }
        });
    }
    
    // === GRAFICO PERFORMANCE === //
    createPerformanceChart() {
        const ctx = document.getElementById('performanceChart');
        if (!ctx) return;
        
        // Calcola metriche performance
        const completionRate = this.stats.active_practices > 0 ? 
            Math.round((this.stats.total_projects / this.stats.active_practices) * 100) : 0;
        const urgencyRate = this.stats.active_practices > 0 ? 
            Math.round((this.stats.pending_deadlines / this.stats.active_practices) * 100) : 0;
        const clientSatisfaction = 85; // Simulato
        const efficiency = 78; // Simulato
        
        const data = {
            labels: ['Completamento', 'Urgenza', 'Soddisfazione', 'Efficienza'],
            datasets: [{
                label: 'Performance %',
                data: [completionRate, urgencyRate, clientSatisfaction, efficiency],
                backgroundColor: [
                    chartColors.success,
                    chartColors.warning,
                    chartColors.info,
                    chartColors.accent
                ],
                borderColor: [
                    chartColors.success,
                    chartColors.warning,
                    chartColors.info,
                    chartColors.accent
                ],
                borderWidth: 2
            }]
        };
        
        this.charts.performance = new Chart(ctx, {
            type: 'radar',
            data: data,
            options: {
                ...commonOptions,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            color: 'rgba(229, 231, 235, 0.5)'
                        },
                        pointLabels: {
                            font: {
                                size: 11,
                                weight: '500'
                            }
                        },
                        ticks: {
                            display: false
                        }
                    }
                },
                plugins: {
                    ...commonOptions.plugins,
                    title: {
                        display: true,
                        text: 'Indicatori Performance',
                        font: {
                            size: 16,
                            weight: '600'
                        },
                        padding: 20
                    }
                }
            }
        });
    }
    
    // === OSSERVA CAMBIAMENTI TEMA === //
    observeThemeChanges() {
        const observer = new MutationObserver(() => {
            this.updateChartsTheme();
        });
        
        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['data-theme']
        });
    }
    
    // === AGGIORNA TEMA GRAFICI === //
    updateChartsTheme() {
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
        
        const textColor = isDark ? 'rgb(229, 231, 235)' : 'rgb(107, 114, 128)';
        const gridColor = isDark ? 'rgba(75, 85, 99, 0.5)' : 'rgba(229, 231, 235, 0.5)';
        
        Object.values(this.charts).forEach(chart => {
            chart.options.plugins.legend.labels.color = textColor;
            chart.options.plugins.title.color = textColor;
            
            if (chart.options.scales) {
                if (chart.options.scales.y) {
                    chart.options.scales.y.grid.color = gridColor;
                    chart.options.scales.y.ticks.color = textColor;
                }
                if (chart.options.scales.x) {
                    chart.options.scales.x.ticks.color = textColor;
                }
                if (chart.options.scales.r) {
                    chart.options.scales.r.grid.color = gridColor;
                    chart.options.scales.r.pointLabels.color = textColor;
                }
            }
            
            chart.update('none');
        });
    }
    
    // === DISTRUGGI GRAFICI === //
    destroy() {
        Object.values(this.charts).forEach(chart => {
            chart.destroy();
        });
        this.charts = {};
    }
}

// === EXPORT PER USO GLOBALE === //
window.DashboardCharts = DashboardCharts;
