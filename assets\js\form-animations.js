/**
 * Form Animations - Sistema di Animazioni per Form
 * Studio Tecnico - Validazione e Feedback Visivo Avanzato
 */

class FormAnimations {
    constructor() {
        this.forms = new Map();
        this.validationRules = new Map();
        this.animationQueue = [];
        this.isProcessing = false;
        
        this.init();
    }
    
    init() {
        this.setupFormListeners();
        this.setupValidationListeners();
        this.setupSubmitAnimations();
        this.initializeExistingForms();
        
        console.log('📝 Form Animations inizializzato');
    }
    
    // === INIZIALIZZAZIONE === //
    initializeExistingForms() {
        document.querySelectorAll('form').forEach(form => {
            this.enhanceForm(form);
        });
    }
    
    enhanceForm(form) {
        const formId = form.id || this.generateFormId();
        form.id = formId;
        
        // Aggiungi classi per animazioni
        form.classList.add('form-animated');
        
        // Setup campi
        this.setupFormFields(form);
        
        // Setup validazione
        this.setupFormValidation(form);
        
        // Salva riferimento
        this.forms.set(formId, {
            element: form,
            fields: new Map(),
            isValid: false,
            isSubmitting: false
        });
        
        // Animazione iniziale
        this.animateFormEntry(form);
    }
    
    setupFormFields(form) {
        const fields = form.querySelectorAll('input, textarea, select');
        
        fields.forEach(field => {
            this.enhanceField(field);
        });
    }
    
    enhanceField(field) {
        const wrapper = this.createFieldWrapper(field);
        
        // Sostituisci il campo con il wrapper
        field.parentNode.insertBefore(wrapper, field);
        wrapper.appendChild(field);
        
        // Setup eventi
        this.setupFieldEvents(field, wrapper);
        
        // Animazione iniziale
        this.animateFieldEntry(wrapper);
    }
    
    createFieldWrapper(field) {
        const wrapper = document.createElement('div');
        wrapper.className = 'field-wrapper';
        
        // Aggiungi indicatori
        const indicator = document.createElement('div');
        indicator.className = 'field-indicator';
        wrapper.appendChild(indicator);
        
        // Aggiungi focus ring
        const focusRing = document.createElement('div');
        focusRing.className = 'field-focus-ring';
        wrapper.appendChild(focusRing);
        
        // Aggiungi messaggio di validazione
        const message = document.createElement('div');
        message.className = 'field-message';
        wrapper.appendChild(message);
        
        return wrapper;
    }
    
    // === EVENT LISTENERS === //
    setupFormListeners() {
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.classList.contains('form-animated')) {
                e.preventDefault();
                this.handleFormSubmit(form);
            }
        });
    }
    
    setupValidationListeners() {
        document.addEventListener('input', (e) => {
            const field = e.target;
            if (field.closest('.form-animated')) {
                this.validateField(field);
            }
        });
        
        document.addEventListener('blur', (e) => {
            const field = e.target;
            if (field.closest('.form-animated')) {
                this.validateField(field, true);
            }
        });
    }
    
    setupSubmitAnimations() {
        document.addEventListener('click', (e) => {
            const button = e.target.closest('button[type="submit"], input[type="submit"]');
            if (button && button.closest('.form-animated')) {
                this.animateSubmitButton(button);
            }
        });
    }
    
    setupFieldEvents(field, wrapper) {
        // Focus events
        field.addEventListener('focus', () => {
            this.animateFieldFocus(wrapper);
        });
        
        field.addEventListener('blur', () => {
            this.animateFieldBlur(wrapper);
        });
        
        // Input events
        field.addEventListener('input', () => {
            this.animateFieldInput(wrapper, field.value);
        });
        
        // Validation events
        field.addEventListener('invalid', () => {
            this.animateFieldError(wrapper);
        });
    }
    
    // === ANIMAZIONI FORM === //
    animateFormEntry(form) {
        form.style.opacity = '0';
        form.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            form.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            form.style.opacity = '1';
            form.style.transform = 'translateY(0)';
        }, 100);
    }
    
    animateFieldEntry(wrapper) {
        const delay = Array.from(wrapper.parentNode.children).indexOf(wrapper) * 100;
        
        wrapper.style.opacity = '0';
        wrapper.style.transform = 'translateX(-20px)';
        
        setTimeout(() => {
            wrapper.style.transition = 'opacity 0.4s ease, transform 0.4s ease';
            wrapper.style.opacity = '1';
            wrapper.style.transform = 'translateX(0)';
        }, delay);
    }
    
    animateFieldFocus(wrapper) {
        wrapper.classList.add('field-focused');
        
        const focusRing = wrapper.querySelector('.field-focus-ring');
        focusRing.style.transform = 'scale(1)';
        focusRing.style.opacity = '1';
        
        // Animazione label floating
        const label = wrapper.querySelector('label');
        if (label) {
            label.classList.add('label-floating');
        }
    }
    
    animateFieldBlur(wrapper) {
        wrapper.classList.remove('field-focused');
        
        const focusRing = wrapper.querySelector('.field-focus-ring');
        focusRing.style.transform = 'scale(0.95)';
        focusRing.style.opacity = '0';
        
        // Rimuovi label floating se campo vuoto
        const field = wrapper.querySelector('input, textarea, select');
        const label = wrapper.querySelector('label');
        if (label && !field.value) {
            label.classList.remove('label-floating');
        }
    }
    
    animateFieldInput(wrapper, value) {
        const indicator = wrapper.querySelector('.field-indicator');
        
        if (value.length > 0) {
            indicator.style.transform = 'scale(1)';
            indicator.style.opacity = '1';
            wrapper.classList.add('field-has-value');
        } else {
            indicator.style.transform = 'scale(0)';
            indicator.style.opacity = '0';
            wrapper.classList.remove('field-has-value');
        }
        
        // Animazione typing
        wrapper.classList.add('field-typing');
        clearTimeout(wrapper.typingTimeout);
        wrapper.typingTimeout = setTimeout(() => {
            wrapper.classList.remove('field-typing');
        }, 500);
    }
    
    animateFieldError(wrapper) {
        wrapper.classList.add('field-error');
        
        // Shake animation
        wrapper.style.animation = 'fieldShake 0.5s ease-in-out';
        
        setTimeout(() => {
            wrapper.style.animation = '';
        }, 500);
        
        // Mostra messaggio errore
        const message = wrapper.querySelector('.field-message');
        message.style.opacity = '1';
        message.style.transform = 'translateY(0)';
    }
    
    animateFieldSuccess(wrapper) {
        wrapper.classList.remove('field-error');
        wrapper.classList.add('field-success');
        
        // Pulse animation
        const indicator = wrapper.querySelector('.field-indicator');
        indicator.style.animation = 'fieldPulse 0.6s ease';
        
        setTimeout(() => {
            indicator.style.animation = '';
        }, 600);
        
        // Nascondi messaggio errore
        const message = wrapper.querySelector('.field-message');
        message.style.opacity = '0';
        message.style.transform = 'translateY(-10px)';
    }
    
    // === VALIDAZIONE === //
    validateField(field, showErrors = false) {
        const wrapper = field.closest('.field-wrapper');
        const isValid = this.checkFieldValidity(field);
        
        if (isValid) {
            this.animateFieldSuccess(wrapper);
        } else if (showErrors) {
            this.animateFieldError(wrapper);
            this.showFieldError(field, wrapper);
        }
        
        return isValid;
    }
    
    checkFieldValidity(field) {
        // Validazione HTML5
        if (!field.checkValidity()) {
            return false;
        }
        
        // Validazioni personalizzate
        const customRules = this.validationRules.get(field.name);
        if (customRules) {
            return customRules.every(rule => rule.test(field.value));
        }
        
        return true;
    }
    
    showFieldError(field, wrapper) {
        const message = wrapper.querySelector('.field-message');
        const errorText = field.validationMessage || 'Campo non valido';
        
        message.textContent = errorText;
        message.classList.add('error-message');
    }
    
    // === SUBMIT === //
    async handleFormSubmit(form) {
        const formData = this.forms.get(form.id);
        if (formData.isSubmitting) return;
        
        // Valida tutti i campi
        const isValid = this.validateForm(form);
        
        if (!isValid) {
            this.animateFormError(form);
            return;
        }
        
        // Avvia animazione submit
        formData.isSubmitting = true;
        await this.animateFormSubmit(form);
        
        // Invia form
        try {
            await this.submitForm(form);
            await this.animateFormSuccess(form);
        } catch (error) {
            await this.animateFormError(form);
            console.error('Errore submit form:', error);
        } finally {
            formData.isSubmitting = false;
        }
    }
    
    validateForm(form) {
        const fields = form.querySelectorAll('input, textarea, select');
        let isValid = true;
        
        fields.forEach(field => {
            if (!this.validateField(field, true)) {
                isValid = false;
            }
        });
        
        return isValid;
    }
    
    async animateFormSubmit(form) {
        // Disabilita form
        this.disableForm(form);
        
        // Mostra loader
        const submitButton = form.querySelector('button[type="submit"], input[type="submit"]');
        if (submitButton) {
            this.showButtonLoader(submitButton);
        }
        
        // Animazione form
        form.style.opacity = '0.7';
        form.style.pointerEvents = 'none';
    }
    
    async animateFormSuccess(form) {
        // Animazione successo
        form.style.animation = 'formSuccess 0.8s ease';
        
        // Mostra messaggio successo
        this.showFormMessage(form, 'Form inviato con successo!', 'success');
        
        setTimeout(() => {
            form.style.animation = '';
            this.enableForm(form);
        }, 800);
    }
    
    async animateFormError(form) {
        // Animazione errore
        form.style.animation = 'formError 0.6s ease';
        
        // Mostra messaggio errore
        this.showFormMessage(form, 'Errore nell\'invio del form', 'error');
        
        setTimeout(() => {
            form.style.animation = '';
            this.enableForm(form);
        }, 600);
    }
    
    // === UTILITY === //
    disableForm(form) {
        const fields = form.querySelectorAll('input, textarea, select, button');
        fields.forEach(field => {
            field.disabled = true;
        });
    }
    
    enableForm(form) {
        const fields = form.querySelectorAll('input, textarea, select, button');
        fields.forEach(field => {
            field.disabled = false;
        });
        
        form.style.opacity = '1';
        form.style.pointerEvents = '';
    }
    
    showButtonLoader(button) {
        const originalText = button.innerHTML;
        button.dataset.originalText = originalText;
        
        button.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2"></span>
            Invio in corso...
        `;
    }
    
    hideButtonLoader(button) {
        const originalText = button.dataset.originalText;
        if (originalText) {
            button.innerHTML = originalText;
        }
    }
    
    showFormMessage(form, text, type) {
        let messageEl = form.querySelector('.form-message');
        
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.className = 'form-message';
            form.insertBefore(messageEl, form.firstChild);
        }
        
        messageEl.textContent = text;
        messageEl.className = `form-message ${type}`;
        messageEl.style.opacity = '1';
        messageEl.style.transform = 'translateY(0)';
        
        // Auto-hide dopo 5 secondi
        setTimeout(() => {
            messageEl.style.opacity = '0';
            messageEl.style.transform = 'translateY(-20px)';
        }, 5000);
    }
    
    async submitForm(form) {
        const formData = new FormData(form);
        
        const response = await fetch(form.action, {
            method: form.method || 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }
        
        return response;
    }
    
    generateFormId() {
        return 'form_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    // === API PUBBLICA === //
    addValidationRule(fieldName, rule) {
        if (!this.validationRules.has(fieldName)) {
            this.validationRules.set(fieldName, []);
        }
        this.validationRules.get(fieldName).push(rule);
    }
    
    removeValidationRule(fieldName) {
        this.validationRules.delete(fieldName);
    }
    
    validateFormById(formId) {
        const form = document.getElementById(formId);
        return form ? this.validateForm(form) : false;
    }
    
    resetForm(formId) {
        const form = document.getElementById(formId);
        if (form) {
            form.reset();
            
            // Reset animazioni
            const wrappers = form.querySelectorAll('.field-wrapper');
            wrappers.forEach(wrapper => {
                wrapper.classList.remove('field-error', 'field-success', 'field-has-value');
            });
        }
    }
}

// === EXPORT PER USO GLOBALE === //
window.FormAnimations = FormAnimations;
