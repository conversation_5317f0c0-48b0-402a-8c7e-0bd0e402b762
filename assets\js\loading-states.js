/**
 * Loading States - Sistema di Stati di Caricamento Avanzati
 * Studio Tecnico - Feedback Visivo per Operazioni Asincrone
 */

class LoadingStates {
    constructor() {
        this.activeLoaders = new Map();
        this.defaultOptions = {
            type: 'spinner', // spinner, skeleton, pulse, dots, bars
            size: 'medium', // small, medium, large
            color: 'primary', // primary, secondary, success, warning, danger
            text: 'Caricamento...',
            overlay: false,
            position: 'center', // center, top, bottom, inline
            timeout: 30000, // 30 secondi timeout
            showProgress: false
        };
        
        this.init();
    }
    
    init() {
        this.createLoaderStyles();
        this.setupGlobalLoaders();
        this.interceptForms();
        this.interceptAjaxRequests();
        
        console.log('⏳ Loading States inizializzato');
    }
    
    // === CREAZIONE LOADER === //
    show(target, options = {}) {
        const config = { ...this.defaultOptions, ...options };
        const loaderId = this.generateLoaderId();
        
        const loader = this.createLoader(loaderId, config);
        const targetElement = typeof target === 'string' ? document.querySelector(target) : target;
        
        if (!targetElement) {
            console.warn('Target element not found for loader');
            return null;
        }
        
        this.attachLoader(targetElement, loader, config);
        this.activeLoaders.set(loaderId, {
            element: loader,
            target: targetElement,
            config: config,
            startTime: Date.now()
        });
        
        // Auto-hide dopo timeout
        if (config.timeout > 0) {
            setTimeout(() => {
                this.hide(loaderId);
            }, config.timeout);
        }
        
        return loaderId;
    }
    
    hide(loaderId) {
        const loaderData = this.activeLoaders.get(loaderId);
        if (!loaderData) return;
        
        const { element, target, config } = loaderData;
        
        // Animazione di uscita
        element.style.opacity = '0';
        element.style.transform = 'scale(0.9)';
        
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            
            // Ripristina target
            if (config.overlay) {
                target.style.pointerEvents = '';
                target.style.position = '';
            }
            
            this.activeLoaders.delete(loaderId);
        }, 300);
    }
    
    hideAll() {
        this.activeLoaders.forEach((_, loaderId) => {
            this.hide(loaderId);
        });
    }
    
    // === CREAZIONE ELEMENTI === //
    createLoader(id, config) {
        const loader = document.createElement('div');
        loader.id = `loader-${id}`;
        loader.className = `loading-state loading-${config.type} loading-${config.size} loading-${config.color}`;
        
        if (config.overlay) {
            loader.classList.add('loading-overlay');
        }
        
        loader.innerHTML = this.getLoaderHTML(config);
        
        // Animazione di entrata
        loader.style.opacity = '0';
        loader.style.transform = 'scale(1.1)';
        
        setTimeout(() => {
            loader.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
            loader.style.opacity = '1';
            loader.style.transform = 'scale(1)';
        }, 10);
        
        return loader;
    }
    
    getLoaderHTML(config) {
        switch (config.type) {
            case 'spinner':
                return this.createSpinnerHTML(config);
            case 'skeleton':
                return this.createSkeletonHTML(config);
            case 'pulse':
                return this.createPulseHTML(config);
            case 'dots':
                return this.createDotsHTML(config);
            case 'bars':
                return this.createBarsHTML(config);
            case 'progress':
                return this.createProgressHTML(config);
            default:
                return this.createSpinnerHTML(config);
        }
    }
    
    createSpinnerHTML(config) {
        return `
            <div class="loader-content">
                <div class="spinner-container">
                    <div class="spinner-ring"></div>
                    <div class="spinner-inner"></div>
                </div>
                ${config.text ? `<div class="loader-text">${config.text}</div>` : ''}
                ${config.showProgress ? '<div class="loader-progress"><div class="progress-bar"></div></div>' : ''}
            </div>
        `;
    }
    
    createSkeletonHTML(config) {
        return `
            <div class="skeleton-container">
                <div class="skeleton-line skeleton-title"></div>
                <div class="skeleton-line skeleton-text"></div>
                <div class="skeleton-line skeleton-text short"></div>
                <div class="skeleton-avatar"></div>
            </div>
        `;
    }
    
    createPulseHTML(config) {
        return `
            <div class="pulse-container">
                <div class="pulse-circle pulse-1"></div>
                <div class="pulse-circle pulse-2"></div>
                <div class="pulse-circle pulse-3"></div>
                ${config.text ? `<div class="loader-text">${config.text}</div>` : ''}
            </div>
        `;
    }
    
    createDotsHTML(config) {
        return `
            <div class="dots-container">
                <div class="dot dot-1"></div>
                <div class="dot dot-2"></div>
                <div class="dot dot-3"></div>
                <div class="dot dot-4"></div>
                ${config.text ? `<div class="loader-text">${config.text}</div>` : ''}
            </div>
        `;
    }
    
    createBarsHTML(config) {
        return `
            <div class="bars-container">
                <div class="bar bar-1"></div>
                <div class="bar bar-2"></div>
                <div class="bar bar-3"></div>
                <div class="bar bar-4"></div>
                <div class="bar bar-5"></div>
                ${config.text ? `<div class="loader-text">${config.text}</div>` : ''}
            </div>
        `;
    }
    
    createProgressHTML(config) {
        return `
            <div class="progress-container">
                <div class="progress-track">
                    <div class="progress-fill"></div>
                </div>
                ${config.text ? `<div class="loader-text">${config.text}</div>` : ''}
                <div class="progress-percentage">0%</div>
            </div>
        `;
    }
    
    // === ATTACHMENT === //
    attachLoader(target, loader, config) {
        if (config.overlay) {
            target.style.position = 'relative';
            target.style.pointerEvents = 'none';
            target.appendChild(loader);
        } else if (config.position === 'inline') {
            target.appendChild(loader);
        } else {
            document.body.appendChild(loader);
            this.positionLoader(loader, target, config.position);
        }
    }
    
    positionLoader(loader, target, position) {
        const rect = target.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;
        
        loader.style.position = 'absolute';
        loader.style.zIndex = '9999';
        
        switch (position) {
            case 'center':
                loader.style.top = (rect.top + scrollTop + rect.height / 2) + 'px';
                loader.style.left = (rect.left + scrollLeft + rect.width / 2) + 'px';
                loader.style.transform = 'translate(-50%, -50%)';
                break;
            case 'top':
                loader.style.top = (rect.top + scrollTop) + 'px';
                loader.style.left = (rect.left + scrollLeft + rect.width / 2) + 'px';
                loader.style.transform = 'translateX(-50%)';
                break;
            case 'bottom':
                loader.style.top = (rect.bottom + scrollTop) + 'px';
                loader.style.left = (rect.left + scrollLeft + rect.width / 2) + 'px';
                loader.style.transform = 'translateX(-50%)';
                break;
        }
    }
    
    // === UTILITY === //
    generateLoaderId() {
        return 'loader_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    updateProgress(loaderId, percentage) {
        const loaderData = this.activeLoaders.get(loaderId);
        if (!loaderData) return;
        
        const progressBar = loaderData.element.querySelector('.progress-fill, .progress-bar');
        const progressText = loaderData.element.querySelector('.progress-percentage');
        
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }
        
        if (progressText) {
            progressText.textContent = Math.round(percentage) + '%';
        }
    }
    
    updateText(loaderId, text) {
        const loaderData = this.activeLoaders.get(loaderId);
        if (!loaderData) return;
        
        const textElement = loaderData.element.querySelector('.loader-text');
        if (textElement) {
            textElement.textContent = text;
        }
    }
    
    // === INTERCEPTORS === //
    interceptForms() {
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (form.classList.contains('loading-form')) {
                const loaderId = this.show(form, {
                    type: 'spinner',
                    text: 'Invio in corso...',
                    overlay: true
                });
                
                // Nascondi loader quando il form viene processato
                form.addEventListener('load', () => this.hide(loaderId), { once: true });
                form.addEventListener('error', () => this.hide(loaderId), { once: true });
            }
        });
    }
    
    interceptAjaxRequests() {
        // Intercetta fetch requests
        const originalFetch = window.fetch;
        window.fetch = (...args) => {
            const request = args[0];
            const options = args[1] || {};
            
            if (options.showLoader !== false) {
                const loaderId = this.show('body', {
                    type: 'spinner',
                    text: 'Caricamento...',
                    position: 'top'
                });
                
                return originalFetch(...args)
                    .finally(() => this.hide(loaderId));
            }
            
            return originalFetch(...args);
        };
    }
    
    setupGlobalLoaders() {
        // Loader per navigazione
        window.addEventListener('beforeunload', () => {
            this.show('body', {
                type: 'spinner',
                text: 'Caricamento pagina...',
                overlay: true
            });
        });
    }
    
    // === PRESET LOADERS === //
    showButtonLoader(button, text = 'Caricamento...') {
        const originalText = button.innerHTML;
        const originalDisabled = button.disabled;
        
        button.disabled = true;
        button.innerHTML = `
            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
            ${text}
        `;
        
        return {
            hide: () => {
                button.disabled = originalDisabled;
                button.innerHTML = originalText;
            }
        };
    }
    
    showTableLoader(table) {
        return this.show(table, {
            type: 'skeleton',
            overlay: true
        });
    }
    
    showFormLoader(form) {
        return this.show(form, {
            type: 'spinner',
            text: 'Elaborazione...',
            overlay: true
        });
    }
    
    showPageLoader() {
        return this.show('body', {
            type: 'spinner',
            text: 'Caricamento pagina...',
            overlay: true,
            size: 'large'
        });
    }
    
    // === SKELETON LOADERS === //
    createTableSkeleton(rows = 5, cols = 4) {
        let html = '<div class="skeleton-table">';
        
        // Header
        html += '<div class="skeleton-row skeleton-header">';
        for (let i = 0; i < cols; i++) {
            html += '<div class="skeleton-cell skeleton-header-cell"></div>';
        }
        html += '</div>';
        
        // Rows
        for (let r = 0; r < rows; r++) {
            html += '<div class="skeleton-row">';
            for (let c = 0; c < cols; c++) {
                html += '<div class="skeleton-cell"></div>';
            }
            html += '</div>';
        }
        
        html += '</div>';
        return html;
    }
    
    createCardSkeleton() {
        return `
            <div class="skeleton-card">
                <div class="skeleton-card-header"></div>
                <div class="skeleton-card-body">
                    <div class="skeleton-line skeleton-title"></div>
                    <div class="skeleton-line skeleton-text"></div>
                    <div class="skeleton-line skeleton-text short"></div>
                </div>
                <div class="skeleton-card-footer"></div>
            </div>
        `;
    }
    
    // === CLEANUP === //
    destroy() {
        this.hideAll();
        this.activeLoaders.clear();
    }
}

// === HELPER FUNCTIONS === //
window.showLoader = function(target, options) {
    if (!window.loadingStates) {
        window.loadingStates = new LoadingStates();
    }
    return window.loadingStates.show(target, options);
};

window.hideLoader = function(loaderId) {
    if (window.loadingStates) {
        window.loadingStates.hide(loaderId);
    }
};

window.showButtonLoader = function(button, text) {
    if (!window.loadingStates) {
        window.loadingStates = new LoadingStates();
    }
    return window.loadingStates.showButtonLoader(button, text);
};

// === EXPORT PER USO GLOBALE === //
window.LoadingStates = LoadingStates;
