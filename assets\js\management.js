/**
 * Management Components - Script per Interazioni Avanzate
 * Studio Tecnico - Sistema di Gestione Moderno
 */

document.addEventListener('DOMContentLoaded', function() {
    // === ELEMENTI DOM === //
    const managementCards = document.querySelectorAll('.management-card');
    const tables = document.querySelectorAll('.table');
    const forms = document.querySelectorAll('form');
    const alerts = document.querySelectorAll('.alert');
    const modals = document.querySelectorAll('.modal');
    
    // === MANAGEMENT CARDS INTERACTIONS === //
    managementCards.forEach(card => {
        // Effetto parallax leggero
        card.addEventListener('mousemove', function(e) {
            const rect = this.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            const rotateX = (y - centerY) / 20;
            const rotateY = (centerX - x) / 20;
            
            this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
        });
        
        // Effetto click con ripple
        card.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            ripple.classList.add('management-ripple');
            
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;
            
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });
    
    // === TABLE ENHANCEMENTS === //
    tables.forEach(table => {
        // Hover effect per righe
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.style.transform = 'scale(1.01)';
                this.style.zIndex = '10';
            });
            
            row.addEventListener('mouseleave', function() {
                this.style.transform = 'scale(1)';
                this.style.zIndex = '1';
            });
        });
        
        // Ordinamento colonne con animazione
        const headers = table.querySelectorAll('thead th');
        headers.forEach(header => {
            if (header.classList.contains('sortable')) {
                header.addEventListener('click', function() {
                    // Aggiungi animazione di ordinamento
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            }
        });
    });
    
    // === FORM ENHANCEMENTS === //
    forms.forEach(form => {
        const inputs = form.querySelectorAll('.form-control');
        
        inputs.forEach(input => {
            // Floating label effect
            input.addEventListener('focus', function() {
                this.parentElement.classList.add('focused');
                this.style.transform = 'translateY(-2px)';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.classList.remove('focused');
                this.style.transform = 'translateY(0)';
                
                // Validazione real-time
                validateInput(this);
            });
            
            // Auto-resize per textarea
            if (input.tagName === 'TEXTAREA') {
                input.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = this.scrollHeight + 'px';
                });
            }
        });
        
        // Form submission con loading
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                showButtonLoading(submitBtn);
            }
        });
    });
    
    // === ALERT AUTO-DISMISS === //
    alerts.forEach(alert => {
        if (alert.classList.contains('alert-dismissible')) {
            // Auto-dismiss dopo 5 secondi
            setTimeout(() => {
                dismissAlert(alert);
            }, 5000);
        }
        
        // Click per dismiss
        const closeBtn = alert.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                dismissAlert(alert);
            });
        }
    });
    
    // === MODAL ENHANCEMENTS === //
    modals.forEach(modal => {
        modal.addEventListener('show.bs.modal', function() {
            // Animazione apertura
            this.style.opacity = '0';
            this.style.transform = 'scale(0.9)';
            
            setTimeout(() => {
                this.style.opacity = '1';
                this.style.transform = 'scale(1)';
            }, 10);
        });
        
        modal.addEventListener('hide.bs.modal', function() {
            // Animazione chiusura
            this.style.opacity = '0';
            this.style.transform = 'scale(0.9)';
        });
    });
    
    // === UTILITY FUNCTIONS === //
    function validateInput(input) {
        const value = input.value.trim();
        const isRequired = input.hasAttribute('required');
        const type = input.getAttribute('type');
        
        // Reset classi
        input.classList.remove('is-valid', 'is-invalid');
        
        // Validazione required
        if (isRequired && !value) {
            input.classList.add('is-invalid');
            showInputFeedback(input, 'Questo campo è obbligatorio', 'invalid');
            return false;
        }
        
        // Validazione email
        if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                input.classList.add('is-invalid');
                showInputFeedback(input, 'Inserisci un indirizzo email valido', 'invalid');
                return false;
            }
        }
        
        // Validazione telefono
        if (input.name === 'telefono' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
            if (!phoneRegex.test(value)) {
                input.classList.add('is-invalid');
                showInputFeedback(input, 'Inserisci un numero di telefono valido', 'invalid');
                return false;
            }
        }
        
        // Input valido
        if (value) {
            input.classList.add('is-valid');
            showInputFeedback(input, 'Campo valido', 'valid');
        }
        
        return true;
    }
    
    function showInputFeedback(input, message, type) {
        // Rimuovi feedback esistente
        const existingFeedback = input.parentElement.querySelector('.valid-feedback, .invalid-feedback');
        if (existingFeedback) {
            existingFeedback.remove();
        }
        
        // Crea nuovo feedback
        const feedback = document.createElement('div');
        feedback.className = type === 'valid' ? 'valid-feedback' : 'invalid-feedback';
        feedback.innerHTML = `<i class="fas fa-${type === 'valid' ? 'check' : 'exclamation-triangle'}"></i> ${message}`;
        
        input.parentElement.appendChild(feedback);
    }
    
    function showButtonLoading(button) {
        const originalText = button.innerHTML;
        button.innerHTML = '<div class="loading-dots"><span></span><span></span><span></span></div> Caricamento...';
        button.disabled = true;
        
        // Ripristina dopo 3 secondi (o quando il form viene processato)
        setTimeout(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        }, 3000);
    }
    
    function dismissAlert(alert) {
        alert.style.opacity = '0';
        alert.style.transform = 'translateX(100%)';
        
        setTimeout(() => {
            alert.remove();
        }, 300);
    }
    
    // === DATATABLES INTEGRATION === //
    if (typeof $.fn.DataTable !== 'undefined') {
        $('.table').DataTable({
            language: {
                url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/it-IT.json'
            },
            responsive: true,
            pageLength: 25,
            dom: '<"row"<"col-sm-6"l><"col-sm-6"f>>rtip',
            drawCallback: function() {
                // Riapplica gli effetti hover dopo il ridisegno
                const rows = this.api().rows().nodes();
                $(rows).off('mouseenter mouseleave').on('mouseenter', function() {
                    $(this).css('transform', 'scale(1.01)');
                }).on('mouseleave', function() {
                    $(this).css('transform', 'scale(1)');
                });
            }
        });
    }
    
    // === PERFORMANCE MONITORING === //
    function measurePerformance() {
        if ('performance' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    if (entry.name.includes('management')) {
                        console.log(`🚀 Management ${entry.name}: ${Math.round(entry.duration)}ms`);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['measure'] });
        }
    }
    
    // === INITIALIZATION === //
    measurePerformance();
    
    console.log('✨ Management Components initialized successfully!');
});

// === CSS DINAMICO PER RIPPLE EFFECT === //
const managementCSS = `
.management-ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(99, 102, 241, 0.2);
    transform: scale(0);
    animation: managementRipple 0.6s linear;
    pointer-events: none;
}

@keyframes managementRipple {
    to {
        transform: scale(2);
        opacity: 0;
    }
}
`;

// Aggiungi CSS dinamico
const style = document.createElement('style');
style.textContent = managementCSS;
document.head.appendChild(style);
