/**
 * Navbar Glassmorphism - Script per Effetti Dinamici
 * Studio Tecnico - Sistema di Navigazione Moderno
 */

document.addEventListener('DOMContentLoaded', function() {
    // === ELEMENTI DOM === //
    const navbar = document.querySelector('.navbar');
    const navLinks = document.querySelectorAll('.nav-link');
    const themeToggle = document.querySelector('.theme-toggle');
    const dropdownMenus = document.querySelectorAll('.dropdown-menu');
    
    // === EFFETTO SCROLL NAVBAR === //
    let lastScrollTop = 0;
    let scrollTimeout;
    
    function handleNavbarScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        // Aggiungi classe scrolled quando si scrolla
        if (scrollTop > 50) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
        
        // Auto-hide navbar su scroll down (mobile)
        if (window.innerWidth <= 768) {
            if (scrollTop > lastScrollTop && scrollTop > 100) {
                // Scrolling down
                navbar.classList.add('navbar-scroll-hidden');
                navbar.classList.remove('navbar-scroll-visible');
            } else {
                // Scrolling up
                navbar.classList.remove('navbar-scroll-hidden');
                navbar.classList.add('navbar-scroll-visible');
            }
        }
        
        lastScrollTop = scrollTop <= 0 ? 0 : scrollTop;
    }
    
    // Throttle scroll events per performance
    function throttleScroll() {
        if (scrollTimeout) {
            return;
        }
        
        scrollTimeout = setTimeout(() => {
            handleNavbarScroll();
            scrollTimeout = null;
        }, 16); // ~60fps
    }
    
    window.addEventListener('scroll', throttleScroll, { passive: true });
    
    // === ACTIVE LINK DETECTION === //
    function updateActiveLink() {
        const currentPath = window.location.pathname;
        
        navLinks.forEach(link => {
            const linkPath = new URL(link.href).pathname;
            
            if (currentPath === linkPath || 
                (linkPath !== '/' && currentPath.startsWith(linkPath))) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    }
    
    // === SMOOTH HOVER EFFECTS === //
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            // Aggiungi effetto ripple
            const ripple = document.createElement('div');
            ripple.classList.add('nav-ripple');
            this.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
        
        // Preload delle pagine al hover per performance
        link.addEventListener('mouseenter', function() {
            const href = this.getAttribute('href');
            if (href && !href.startsWith('#') && !href.startsWith('javascript:')) {
                const linkPreload = document.createElement('link');
                linkPreload.rel = 'prefetch';
                linkPreload.href = href;
                document.head.appendChild(linkPreload);
            }
        });
    });
    
    // === THEME TOGGLE ANIMATION === //
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            // Aggiungi animazione di rotazione
            this.style.transform = 'scale(0.9) rotate(180deg)';
            
            setTimeout(() => {
                this.style.transform = 'scale(1) rotate(0deg)';
            }, 200);
            
            // Effetto particelle (opzionale)
            createThemeParticles(this);
        });
    }
    
    // === DROPDOWN ANIMATIONS === //
    dropdownMenus.forEach(menu => {
        const dropdown = menu.closest('.dropdown');
        const toggle = dropdown.querySelector('.dropdown-toggle');
        
        // Animazione apertura
        dropdown.addEventListener('show.bs.dropdown', function() {
            menu.style.opacity = '0';
            menu.style.transform = 'translateY(-10px) scale(0.95)';
            
            setTimeout(() => {
                menu.style.opacity = '1';
                menu.style.transform = 'translateY(0) scale(1)';
            }, 10);
        });
        
        // Animazione chiusura
        dropdown.addEventListener('hide.bs.dropdown', function() {
            menu.style.opacity = '0';
            menu.style.transform = 'translateY(-10px) scale(0.95)';
        });
    });
    
    // === SEARCH BAR ENHANCEMENT === //
    const searchInput = document.querySelector('.navbar-search input');
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
            this.parentElement.style.boxShadow = 'var(--shadow-lg)';
        });
        
        searchInput.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
            this.parentElement.style.boxShadow = 'none';
        });
        
        // Search suggestions (placeholder per future implementation)
        searchInput.addEventListener('input', function() {
            const query = this.value.trim();
            if (query.length > 2) {
                // Qui si può implementare la ricerca in tempo reale
                console.log('🔍 Searching for:', query);
            }
        });
    }
    
    // === MOBILE MENU ENHANCEMENTS === //
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');
    
    if (navbarToggler && navbarCollapse) {
        navbarToggler.addEventListener('click', function() {
            // Aggiungi animazione hamburger
            this.classList.toggle('active');
            
            // Effetto blur background quando menu è aperto
            if (navbarCollapse.classList.contains('show')) {
                document.body.style.overflow = 'hidden';
                document.body.style.filter = 'blur(2px)';
            } else {
                document.body.style.overflow = '';
                document.body.style.filter = '';
            }
        });
        
        // Chiudi menu quando si clicca su un link
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth <= 992) {
                    navbarCollapse.classList.remove('show');
                    navbarToggler.classList.remove('active');
                    document.body.style.overflow = '';
                    document.body.style.filter = '';
                }
            });
        });
    }
    
    // === UTILITY FUNCTIONS === //
    function createThemeParticles(element) {
        const rect = element.getBoundingClientRect();
        const particles = 6;
        
        for (let i = 0; i < particles; i++) {
            const particle = document.createElement('div');
            particle.style.cssText = `
                position: fixed;
                width: 4px;
                height: 4px;
                background: var(--primary-500);
                border-radius: 50%;
                pointer-events: none;
                z-index: 9999;
                left: ${rect.left + rect.width / 2}px;
                top: ${rect.top + rect.height / 2}px;
            `;
            
            document.body.appendChild(particle);
            
            // Animazione particelle
            const angle = (i / particles) * Math.PI * 2;
            const distance = 30;
            const x = Math.cos(angle) * distance;
            const y = Math.sin(angle) * distance;
            
            particle.animate([
                { transform: 'translate(0, 0) scale(1)', opacity: 1 },
                { transform: `translate(${x}px, ${y}px) scale(0)`, opacity: 0 }
            ], {
                duration: 600,
                easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
            }).onfinish = () => particle.remove();
        }
    }
    
    // === PERFORMANCE MONITORING === //
    function measureNavbarPerformance() {
        if ('performance' in window) {
            const observer = new PerformanceObserver((list) => {
                list.getEntries().forEach((entry) => {
                    if (entry.name.includes('navbar')) {
                        console.log(`🚀 Navbar ${entry.name}: ${Math.round(entry.duration)}ms`);
                    }
                });
            });
            
            observer.observe({ entryTypes: ['measure'] });
        }
    }
    
    // === INITIALIZATION === //
    updateActiveLink();
    measureNavbarPerformance();
    
    // Update active link on navigation
    window.addEventListener('popstate', updateActiveLink);
    
    // Resize handler
    window.addEventListener('resize', () => {
        if (window.innerWidth > 768) {
            navbar.classList.remove('navbar-scroll-hidden', 'navbar-scroll-visible');
            document.body.style.overflow = '';
            document.body.style.filter = '';
        }
    });
    
    console.log('✨ Navbar Glassmorphism initialized successfully!');
});

// === CSS DINAMICO PER RIPPLE EFFECT === //
const rippleCSS = `
.nav-ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(99, 102, 241, 0.3);
    transform: scale(0);
    animation: navRipple 0.6s linear;
    pointer-events: none;
    width: 100px;
    height: 100px;
    left: 50%;
    top: 50%;
    margin-left: -50px;
    margin-top: -50px;
}

@keyframes navRipple {
    to {
        transform: scale(2);
        opacity: 0;
    }
}
`;

// Aggiungi CSS dinamico
const style = document.createElement('style');
style.textContent = rippleCSS;
document.head.appendChild(style);
