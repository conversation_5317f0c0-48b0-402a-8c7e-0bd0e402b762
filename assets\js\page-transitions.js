/**
 * Page Transitions - Sistema di Transizioni Fluide
 * Studio Tecnico - Animazioni di Navigazione Avanzate
 */

class PageTransitions {
    constructor() {
        this.isTransitioning = false;
        this.transitionDuration = 600;
        this.currentPage = window.location.pathname;
        this.transitionTypes = {
            'fade': this.fadeTransition,
            'slide': this.slideTransition,
            'scale': this.scaleTransition,
            'flip': this.flipTransition
        };
        this.defaultTransition = 'fade';
        
        this.init();
    }
    
    init() {
        this.createTransitionOverlay();
        this.setupEventListeners();
        this.setupPageLoadAnimation();
        this.setupProgressBar();
        
        console.log('🎬 Page Transitions inizializzato');
    }
    
    // === OVERLAY TRANSIZIONE === //
    createTransitionOverlay() {
        const overlay = document.createElement('div');
        overlay.id = 'page-transition-overlay';
        overlay.className = 'page-transition-overlay';
        document.body.appendChild(overlay);
        
        // Aggiungi loader
        const loader = document.createElement('div');
        loader.className = 'page-loader';
        loader.innerHTML = `
            <div class="loader-content">
                <div class="loader-spinner"></div>
                <div class="loader-text">Caricamento...</div>
                <div class="loader-progress">
                    <div class="loader-progress-bar"></div>
                </div>
            </div>
        `;
        overlay.appendChild(loader);
    }
    
    // === PROGRESS BAR === //
    setupProgressBar() {
        const progressBar = document.createElement('div');
        progressBar.id = 'page-progress-bar';
        progressBar.className = 'page-progress-bar';
        document.body.appendChild(progressBar);
    }
    
    // === EVENT LISTENERS === //
    setupEventListeners() {
        // Intercetta click sui link interni
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (this.shouldInterceptLink(link)) {
                e.preventDefault();
                this.navigateToPage(link.href, link.dataset.transition);
            }
        });
        
        // Intercetta submit dei form
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (this.shouldInterceptForm(form)) {
                e.preventDefault();
                this.submitForm(form);
            }
        });
        
        // Gestisci back/forward del browser
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.transitioned) {
                this.handlePopState(e.state);
            }
        });
        
        // Preload delle pagine al hover
        document.addEventListener('mouseenter', (e) => {
            const link = e.target.closest('a');
            if (this.shouldPreloadLink(link)) {
                this.preloadPage(link.href);
            }
        }, true);
    }
    
    // === VALIDAZIONE LINK === //
    shouldInterceptLink(link) {
        if (!link || !link.href) return false;
        if (link.target === '_blank') return false;
        if (link.href.includes('#')) return false;
        if (link.href.includes('mailto:') || link.href.includes('tel:')) return false;
        if (link.classList.contains('no-transition')) return false;
        if (link.download) return false;
        
        // Solo link interni
        const linkUrl = new URL(link.href);
        const currentUrl = new URL(window.location.href);
        return linkUrl.origin === currentUrl.origin;
    }
    
    shouldInterceptForm(form) {
        return !form.classList.contains('no-transition') && 
               !form.enctype?.includes('multipart/form-data');
    }
    
    shouldPreloadLink(link) {
        return this.shouldInterceptLink(link) && 
               !link.classList.contains('no-preload');
    }
    
    // === NAVIGAZIONE === //
    async navigateToPage(url, transitionType = null) {
        if (this.isTransitioning) return;
        
        this.isTransitioning = true;
        const transition = transitionType || this.defaultTransition;
        
        try {
            // Mostra progress bar
            this.showProgressBar();
            
            // Avvia transizione di uscita
            await this.startExitTransition(transition);
            
            // Carica nuova pagina
            const response = await this.fetchPage(url);
            
            // Aggiorna URL e stato
            this.updateHistory(url, transition);
            
            // Sostituisci contenuto
            await this.replacePage(response);
            
            // Avvia transizione di entrata
            await this.startEnterTransition(transition);
            
        } catch (error) {
            console.error('Errore durante la transizione:', error);
            // Fallback alla navigazione normale
            window.location.href = url;
        } finally {
            this.hideProgressBar();
            this.isTransitioning = false;
        }
    }
    
    // === FETCH PAGINA === //
    async fetchPage(url) {
        const response = await fetch(url, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'text/html'
            }
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        return await response.text();
    }
    
    // === SOSTITUZIONE PAGINA === //
    async replacePage(html) {
        const parser = new DOMParser();
        const newDoc = parser.parseFromString(html, 'text/html');
        
        // Aggiorna title
        document.title = newDoc.title;
        
        // Aggiorna meta tags
        this.updateMetaTags(newDoc);
        
        // Sostituisci contenuto principale
        const newMain = newDoc.querySelector('main') || newDoc.querySelector('.container');
        const currentMain = document.querySelector('main') || document.querySelector('.container');
        
        if (newMain && currentMain) {
            currentMain.innerHTML = newMain.innerHTML;
        } else {
            // Fallback: sostituisci body
            document.body.innerHTML = newDoc.body.innerHTML;
        }
        
        // Reinizializza script
        this.reinitializeScripts();
        
        // Aggiorna breadcrumb se presente
        this.updateBreadcrumb();
    }
    
    // === TRANSIZIONI === //
    async startExitTransition(type) {
        const overlay = document.getElementById('page-transition-overlay');
        const main = document.querySelector('main') || document.querySelector('.container');
        
        overlay.style.display = 'flex';
        
        switch (type) {
            case 'fade':
                await this.fadeOut(main, overlay);
                break;
            case 'slide':
                await this.slideOut(main, overlay);
                break;
            case 'scale':
                await this.scaleOut(main, overlay);
                break;
            case 'flip':
                await this.flipOut(main, overlay);
                break;
            default:
                await this.fadeOut(main, overlay);
        }
    }
    
    async startEnterTransition(type) {
        const overlay = document.getElementById('page-transition-overlay');
        const main = document.querySelector('main') || document.querySelector('.container');
        
        switch (type) {
            case 'fade':
                await this.fadeIn(main, overlay);
                break;
            case 'slide':
                await this.slideIn(main, overlay);
                break;
            case 'scale':
                await this.scaleIn(main, overlay);
                break;
            case 'flip':
                await this.flipIn(main, overlay);
                break;
            default:
                await this.fadeIn(main, overlay);
        }
        
        overlay.style.display = 'none';
    }
    
    // === ANIMAZIONI SPECIFICHE === //
    fadeOut(element, overlay) {
        return new Promise(resolve => {
            element.style.transition = `opacity ${this.transitionDuration}ms ease`;
            overlay.style.transition = `opacity ${this.transitionDuration}ms ease`;
            
            element.style.opacity = '0';
            overlay.style.opacity = '1';
            
            setTimeout(resolve, this.transitionDuration);
        });
    }
    
    fadeIn(element, overlay) {
        return new Promise(resolve => {
            element.style.opacity = '0';
            
            setTimeout(() => {
                element.style.transition = `opacity ${this.transitionDuration}ms ease`;
                overlay.style.transition = `opacity ${this.transitionDuration}ms ease`;
                
                element.style.opacity = '1';
                overlay.style.opacity = '0';
                
                setTimeout(() => {
                    element.style.transition = '';
                    resolve();
                }, this.transitionDuration);
            }, 50);
        });
    }
    
    slideOut(element, overlay) {
        return new Promise(resolve => {
            element.style.transition = `transform ${this.transitionDuration}ms ease`;
            overlay.style.transition = `opacity ${this.transitionDuration}ms ease`;
            
            element.style.transform = 'translateX(-100%)';
            overlay.style.opacity = '1';
            
            setTimeout(resolve, this.transitionDuration);
        });
    }
    
    slideIn(element, overlay) {
        return new Promise(resolve => {
            element.style.transform = 'translateX(100%)';
            element.style.opacity = '1';
            
            setTimeout(() => {
                element.style.transition = `transform ${this.transitionDuration}ms ease`;
                overlay.style.transition = `opacity ${this.transitionDuration}ms ease`;
                
                element.style.transform = 'translateX(0)';
                overlay.style.opacity = '0';
                
                setTimeout(() => {
                    element.style.transition = '';
                    element.style.transform = '';
                    resolve();
                }, this.transitionDuration);
            }, 50);
        });
    }
    
    scaleOut(element, overlay) {
        return new Promise(resolve => {
            element.style.transition = `transform ${this.transitionDuration}ms ease, opacity ${this.transitionDuration}ms ease`;
            overlay.style.transition = `opacity ${this.transitionDuration}ms ease`;
            
            element.style.transform = 'scale(0.8)';
            element.style.opacity = '0';
            overlay.style.opacity = '1';
            
            setTimeout(resolve, this.transitionDuration);
        });
    }
    
    scaleIn(element, overlay) {
        return new Promise(resolve => {
            element.style.transform = 'scale(1.2)';
            element.style.opacity = '0';
            
            setTimeout(() => {
                element.style.transition = `transform ${this.transitionDuration}ms ease, opacity ${this.transitionDuration}ms ease`;
                overlay.style.transition = `opacity ${this.transitionDuration}ms ease`;
                
                element.style.transform = 'scale(1)';
                element.style.opacity = '1';
                overlay.style.opacity = '0';
                
                setTimeout(() => {
                    element.style.transition = '';
                    element.style.transform = '';
                    resolve();
                }, this.transitionDuration);
            }, 50);
        });
    }
    
    // === UTILITY === //
    updateHistory(url, transition) {
        const state = {
            transitioned: true,
            transition: transition,
            timestamp: Date.now()
        };
        
        history.pushState(state, '', url);
        this.currentPage = url;
    }
    
    updateMetaTags(newDoc) {
        // Aggiorna meta description
        const newMeta = newDoc.querySelector('meta[name="description"]');
        const currentMeta = document.querySelector('meta[name="description"]');
        if (newMeta && currentMeta) {
            currentMeta.content = newMeta.content;
        }
    }
    
    reinitializeScripts() {
        // Reinizializza componenti JavaScript
        if (window.advancedTables) {
            window.advancedTables.destroy();
            window.advancedTables = new AdvancedTables();
        }
        
        if (window.breadcrumbManager) {
            window.breadcrumbManager = new BreadcrumbManager();
        }
        
        // Reinizializza tooltip
        const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(el => new bootstrap.Tooltip(el));
    }
    
    updateBreadcrumb() {
        // Aggiorna breadcrumb se presente
        const breadcrumbNav = document.querySelector('.breadcrumb-nav');
        if (breadcrumbNav && window.breadcrumbManager) {
            window.breadcrumbManager.refresh();
        }
    }
    
    showProgressBar() {
        const progressBar = document.getElementById('page-progress-bar');
        progressBar.style.width = '0%';
        progressBar.style.opacity = '1';
        
        // Simula progresso
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 30;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
        }, 100);
        
        this.progressInterval = interval;
    }
    
    hideProgressBar() {
        const progressBar = document.getElementById('page-progress-bar');
        progressBar.style.width = '100%';
        
        setTimeout(() => {
            progressBar.style.opacity = '0';
            clearInterval(this.progressInterval);
        }, 200);
    }
    
    setupPageLoadAnimation() {
        // Animazione iniziale della pagina
        document.addEventListener('DOMContentLoaded', () => {
            const main = document.querySelector('main') || document.querySelector('.container');
            if (main) {
                main.style.opacity = '0';
                main.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    main.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                    main.style.opacity = '1';
                    main.style.transform = 'translateY(0)';
                }, 100);
            }
        });
    }
    
    // === PRELOAD === //
    preloadPage(url) {
        if (this.preloadedPages?.has(url)) return;
        
        if (!this.preloadedPages) {
            this.preloadedPages = new Set();
        }
        
        fetch(url, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        }).then(() => {
            this.preloadedPages.add(url);
        }).catch(() => {
            // Ignora errori di preload
        });
    }
    
    // === FORM SUBMISSION === //
    async submitForm(form) {
        if (this.isTransitioning) return;
        
        this.isTransitioning = true;
        
        try {
            this.showProgressBar();
            
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: form.method || 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            });
            
            if (response.redirected) {
                await this.navigateToPage(response.url);
            } else {
                const html = await response.text();
                await this.replacePage(html);
            }
            
        } catch (error) {
            console.error('Errore durante il submit:', error);
            form.submit(); // Fallback
        } finally {
            this.hideProgressBar();
            this.isTransitioning = false;
        }
    }
}

// === EXPORT PER USO GLOBALE === //
window.PageTransitions = PageTransitions;
