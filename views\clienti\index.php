<?php
$pageTitle = "Gestione Clienti";
include VIEWS_DIR . '/layouts/header.php';
?>

<link rel="stylesheet" href="<?= rtrim(BASE_URL, '/') ?>/assets/css/management.css">

<div class="container-fluid py-4">
    <div class="page-header">
        <h2 class="page-title">
            <i class="fas fa-users"></i>
            Gestione Clienti
        </h2>
        <div class="page-actions">
            <?php 
            $guideTitle = "Guida Rapida - Gestione Clienti";
            $guideContent = '
                <div class="guide-section mb-4">
                    <h6 class="fw-bold"><i class="fas fa-users me-2"></i>Gestione Clienti</h6>
                    <p>Questa sezione ti permette di gestire l\'anagrafica completa dei clienti:</p>
                    <ul class="list-unstyled ps-3">
                        <li><i class="fas fa-plus text-success me-2"></i><strong>Nuovo Cliente:</strong> Aggiungi un nuovo cliente (privato o azienda)</li>
                        <li><i class="fas fa-edit text-primary me-2"></i><strong>Modifica:</strong> Aggiorna i dati dei clienti esistenti</li>
                        <li><i class="fas fa-search text-info me-2"></i><strong>Ricerca:</strong> Usa i filtri per trovare rapidamente i clienti</li>
                        <li><i class="fas fa-file-alt text-warning me-2"></i><strong>Progetti:</strong> Visualizza i progetti associati ad ogni cliente</li>
                    </ul>
                </div>
                <div class="guide-section">
                    <h6 class="fw-bold"><i class="fas fa-lightbulb me-2"></i>Suggerimenti</h6>
                    <ul class="list-unstyled ps-3">
                        <li><i class="fas fa-check text-success me-2"></i>Mantieni sempre aggiornati i contatti dei clienti</li>
                        <li><i class="fas fa-check text-success me-2"></i>Verifica la correttezza dei codici fiscali/P.IVA</li>
                        <li><i class="fas fa-check text-success me-2"></i>Controlla regolarmente lo stato dei progetti associati</li>
                    </ul>
                </div>
            ';
            include VIEWS_DIR . '/components/quick_guide.php'; 
            ?>
            <a href="<?= rtrim(BASE_URL, '/') ?>/clienti/nuovo" class="btn btn-neutral">
                <i class="fas fa-plus"></i>
                Nuovo Cliente
            </a>
        </div>
    </div>

    <div class="container mt-4">
        <?php if (isset($_GET['error'])): ?>
            <?php 
            $errorMessage = '';
            switch ($_GET['error']) {
                case 'cliente_non_trovato':
                    $errorMessage = 'Cliente non trovato.';
                    break;
                case 'impossibile_eliminare_cliente_con_progetti':
                    $errorMessage = 'Impossibile eliminare il cliente perché ha dei progetti collegati.';
                    break;
                case 'errore_eliminazione':
                    $errorMessage = 'Si è verificato un errore durante l\'eliminazione del cliente.';
                    break;
                default:
                    $errorMessage = 'Si è verificato un errore.';
            }
            ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $errorMessage ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['success'])): ?>
            <?php 
            $successMessage = '';
            switch ($_GET['success']) {
                case 'cliente_eliminato':
                    $successMessage = 'Cliente eliminato con successo.';
                    break;
                default:
                    $successMessage = 'Operazione completata con successo.';
            }
            ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $successMessage ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
    </div>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($_SESSION['error']) ?>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <?php if (isset($_GET['msg'])): ?>
        <?php if ($_GET['msg'] === 'success'): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Cliente inserito con successo!
            </div>
        <?php elseif ($_GET['msg'] === 'update_success'): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Cliente aggiornato con successo!
            </div>
        <?php elseif ($_GET['msg'] === 'delete_success'): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i>
                Cliente eliminato con successo!
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-circle"></i>
            <?= htmlspecialchars($error) ?>
        </div>
    <?php endif; ?>

    <?php if (empty($clienti)): ?>
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            Nessun cliente trovato. <a href="<?= BASE_URL ?>clienti/nuovo" class="alert-link">Aggiungi il primo cliente</a>
        </div>
    <?php else: ?>
        <?php
        // Configurazione tabella clienti avanzata
        $tableId = 'clienti-table';
        $title = 'Gestione Clienti';
        $subtitle = 'Elenco completo di tutti i clienti registrati nel sistema';
        $tableType = 'clienti';
        $showFilters = true;
        $showExport = true;

        // Definizione colonne
        $columns = [
            [
                'title' => 'Tipo',
                'data' => 'tipo_cliente',
                'class' => 'text-center',
                'width' => '100px',
                'type' => 'badge',
                'badgeClass' => function($value) {
                    return $value === 'privato' ? 'primary' : 'success';
                },
                'icon' => 'fas fa-tag'
            ],
            [
                'title' => 'Nome/Ragione Sociale',
                'data' => 'nome_completo',
                'class' => '',
                'icon' => 'fas fa-user'
            ],
            [
                'title' => 'Email',
                'data' => 'email',
                'class' => '',
                'icon' => 'fas fa-envelope'
            ],
            [
                'title' => 'Telefono',
                'data' => 'telefono',
                'class' => '',
                'icon' => 'fas fa-phone'
            ],
            [
                'title' => 'Città',
                'data' => 'citta',
                'class' => '',
                'icon' => 'fas fa-map-marker-alt'
            ],
            [
                'title' => 'Progetti',
                'data' => 'num_progetti',
                'class' => 'text-center',
                'width' => '80px',
                'type' => 'badge',
                'badgeClass' => function($value) {
                    return 'info';
                },
                'icon' => 'fas fa-project-diagram'
            ],
            [
                'title' => 'Pratiche',
                'data' => 'num_pratiche',
                'class' => 'text-center',
                'width' => '80px',
                'type' => 'badge',
                'badgeClass' => function($value) {
                    return 'warning';
                },
                'icon' => 'fas fa-folder-open'
            ],
            [
                'title' => 'Azioni',
                'data' => 'id',
                'class' => 'text-end no-export',
                'width' => '150px',
                'orderable' => 'false',
                'type' => 'actions',
                'actions' => [
                    [
                        'url' => BASE_URL . 'clienti/dettagli/{id}',
                        'class' => 'btn-outline-primary',
                        'icon' => 'fas fa-eye',
                        'title' => 'Visualizza dettagli'
                    ],
                    [
                        'url' => BASE_URL . 'clienti/modifica/{id}',
                        'class' => 'btn-outline-secondary',
                        'icon' => 'fas fa-edit',
                        'title' => 'Modifica cliente'
                    ],
                    [
                        'url' => 'javascript:confirmDelete({id})',
                        'class' => 'btn-outline-danger',
                        'icon' => 'fas fa-trash',
                        'title' => 'Elimina cliente'
                    ]
                ]
            ]
        ];

        // Prepara i dati per la tabella
        $tableData = [];
        foreach ($clienti as $cliente) {
            $tableData[] = [
                'id' => $cliente['id'],
                'tipo_cliente' => ucfirst($cliente['tipo_cliente']),
                'nome_completo' => $cliente['tipo_cliente'] === 'privato'
                    ? $cliente['cognome'] . ' ' . $cliente['nome']
                    : $cliente['ragione_sociale'],
                'email' => $cliente['email'],
                'telefono' => $cliente['telefono'],
                'citta' => $cliente['citta'],
                'num_progetti' => $cliente['num_progetti'],
                'num_pratiche' => $cliente['num_pratiche']
            ];
        }

        // Includi il componente tabella avanzata
        $data = $tableData;
        include VIEWS_DIR . '/components/advanced-table.php';
        ?>
    <?php endif; ?>
</div>

<script>
// Definisci BASE_URL per JavaScript
const BASE_URL = '<?= BASE_URL ?>';

// Funzione per confermare eliminazione
function confirmDelete(id) {
    Swal.fire({
        title: 'Elimina Cliente',
        text: "Sei sicuro di voler eliminare questo cliente? Questa azione non può essere annullata!",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#EF4444',
        cancelButtonColor: '#6B7280',
        confirmButtonText: '<i class="fas fa-trash me-2"></i>Sì, elimina!',
        cancelButtonText: '<i class="fas fa-times me-2"></i>Annulla',
        customClass: {
            popup: 'swal-modern',
            confirmButton: 'btn btn-danger',
            cancelButton: 'btn btn-secondary'
        },
        buttonsStyling: false,
        showLoaderOnConfirm: true,
        preConfirm: () => {
            return fetch(`${BASE_URL}clienti/elimina/${id}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            }).then(response => {
                if (!response.ok) {
                    throw new Error('Errore durante l\'eliminazione');
                }
                return response.json();
            }).catch(error => {
                Swal.showValidationMessage(`Errore: ${error.message}`);
            });
        }
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'Eliminato!',
                text: 'Il cliente è stato eliminato con successo.',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            }).then(() => {
                // Ricarica la tabella o la pagina
                if (window.advancedTables) {
                    window.advancedTables.refreshTable('clienti-table');
                } else {
                    window.location.reload();
                }
            });
        }
    });
}

// Inizializzazione specifica per la pagina clienti
document.addEventListener('DOMContentLoaded', function() {
    // Aggiungi tooltip per le azioni
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Personalizzazioni specifiche per la tabella clienti
    if (window.advancedTables) {
        const clientiTable = window.advancedTables.getTable('clienti-table');
        if (clientiTable) {
            // Aggiungi eventi personalizzati
            clientiTable.on('draw', function() {
                console.log('📊 Tabella clienti ridisegnata');
            });
        }
    }

    console.log('👥 Pagina clienti inizializzata');
});
</script>

<style>
/* === STILI SPECIFICI PAGINA CLIENTI === */
.swal-modern {
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-2xl);
}

.swal-modern .swal2-title {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
}

.swal-modern .swal2-content {
    font-size: var(--text-base);
    color: var(--text-secondary);
}

/* === BADGE PERSONALIZZATI === */
.badge-primary {
    background: var(--gradient-primary);
    color: white;
}

.badge-success {
    background: var(--gradient-secondary);
    color: white;
}

.badge-info {
    background: var(--gradient-accent);
    color: white;
}

.badge-warning {
    background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
    color: white;
}

/* === RESPONSIVE CLIENTI === */
@media (max-width: 768px) {
    .table-header-actions {
        flex-direction: column;
        gap: var(--space-2);
        width: 100%;
    }

    .table-header-actions .btn {
        width: 100%;
        justify-content: center;
    }
}
</style>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>