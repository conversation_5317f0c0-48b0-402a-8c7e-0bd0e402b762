<?php
/**
 * Componente Advanced Table
 * Studio Tecnico - Wrapper per Tabelle DataTables Modernizzate
 */

// Parametri del componente
$tableId = $tableId ?? 'advanced-table-' . uniqid();
$tableClass = $tableClass ?? 'table table-striped table-hover';
$columns = $columns ?? [];
$data = $data ?? [];
$showFilters = $showFilters ?? true;
$showExport = $showExport ?? true;
$tableType = $tableType ?? 'default'; // clienti, progetti, pratiche, default
$title = $title ?? 'Tabella Dati';
$subtitle = $subtitle ?? '';
?>

<div class="advanced-table-wrapper">
    <!-- Header Tabella -->
    <div class="table-header">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h3 class="table-title">
                    <i class="fas fa-table me-2"></i>
                    <?= htmlspecialchars($title) ?>
                </h3>
                <?php if ($subtitle): ?>
                    <p class="table-subtitle text-secondary mb-0"><?= htmlspecialchars($subtitle) ?></p>
                <?php endif; ?>
            </div>
            <div class="table-header-actions">
                <?php if ($showFilters): ?>
                    <button class="btn btn-outline-secondary btn-sm toggle-filters" type="button">
                        <i class="fas fa-filter me-2"></i>
                        Filtri
                    </button>
                <?php endif; ?>
                <button class="btn btn-outline-primary btn-sm table-refresh" type="button">
                    <i class="fas fa-sync-alt me-2"></i>
                    Aggiorna
                </button>
            </div>
        </div>
    </div>

    <!-- Contenitore Tabella -->
    <div class="table-container <?= $tableType ?>-table">
        <table id="<?= htmlspecialchars($tableId) ?>" class="<?= htmlspecialchars($tableClass) ?>">
            <thead>
                <tr>
                    <?php foreach ($columns as $column): ?>
                        <th class="<?= $column['class'] ?? '' ?>" 
                            data-orderable="<?= $column['orderable'] ?? 'true' ?>"
                            <?= isset($column['width']) ? 'style="width: ' . $column['width'] . '"' : '' ?>>
                            <?php if (isset($column['icon'])): ?>
                                <i class="<?= htmlspecialchars($column['icon']) ?> me-2"></i>
                            <?php endif; ?>
                            <?= htmlspecialchars($column['title']) ?>
                        </th>
                    <?php endforeach; ?>
                </tr>
            </thead>
            <tbody>
                <?php if (!empty($data)): ?>
                    <?php foreach ($data as $row): ?>
                        <tr>
                            <?php foreach ($columns as $index => $column): ?>
                                <td class="<?= $column['class'] ?? '' ?>">
                                    <?php
                                    $value = $row[$column['data']] ?? '';
                                    
                                    // Rendering personalizzato basato sul tipo
                                    if (isset($column['render'])) {
                                        echo $column['render']($value, $row);
                                    } elseif ($column['type'] ?? '' === 'badge') {
                                        $badgeClass = $column['badgeClass']($value) ?? 'secondary';
                                        echo '<span class="badge badge-' . $badgeClass . '">' . htmlspecialchars($value) . '</span>';
                                    } elseif ($column['type'] ?? '' === 'status') {
                                        $statusClass = str_replace(' ', '_', strtolower($value));
                                        echo '<div class="d-flex align-items-center">';
                                        echo '<span class="status-indicator status-' . $statusClass . '"></span>';
                                        echo '<span>' . htmlspecialchars($value) . '</span>';
                                        echo '</div>';
                                    } elseif ($column['type'] ?? '' === 'currency') {
                                        echo '€ ' . number_format((float)$value, 2, ',', '.');
                                    } elseif ($column['type'] ?? '' === 'date') {
                                        if ($value && $value !== '0000-00-00') {
                                            $date = new DateTime($value);
                                            echo $date->format('d/m/Y');
                                        } else {
                                            echo '-';
                                        }
                                    } elseif ($column['type'] ?? '' === 'actions') {
                                        // Rendering azioni
                                        echo '<div class="table-actions">';
                                        if (isset($column['actions'])) {
                                            foreach ($column['actions'] as $action) {
                                                $url = str_replace('{id}', $row['id'] ?? '', $action['url']);
                                                $class = $action['class'] ?? 'btn-outline-primary';
                                                $icon = $action['icon'] ?? 'fas fa-eye';
                                                $title = $action['title'] ?? '';
                                                
                                                echo '<a href="' . htmlspecialchars($url) . '" ';
                                                echo 'class="btn ' . htmlspecialchars($class) . ' btn-sm" ';
                                                echo 'title="' . htmlspecialchars($title) . '">';
                                                echo '<i class="' . htmlspecialchars($icon) . '"></i>';
                                                echo '</a>';
                                            }
                                        }
                                        echo '</div>';
                                    } else {
                                        echo htmlspecialchars($value);
                                    }
                                    ?>
                                </td>
                            <?php endforeach; ?>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<style>
/* === ADVANCED TABLE WRAPPER === */
.advanced-table-wrapper {
    background: var(--surface-color);
    border-radius: var(--radius-xl);
    padding: var(--space-6);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--neutral-200);
    margin-bottom: var(--space-6);
}

.table-header {
    border-bottom: 1px solid var(--neutral-200);
    padding-bottom: var(--space-4);
    margin-bottom: var(--space-6);
}

.table-title {
    font-size: var(--text-xl);
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
}

.table-title i {
    color: var(--primary-500);
    padding: var(--space-2);
    background: var(--primary-50);
    border-radius: var(--radius-lg);
}

.table-subtitle {
    font-size: var(--text-sm);
    margin-top: var(--space-1);
}

.table-header-actions {
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

.table-header-actions .btn {
    border-radius: var(--radius-lg);
    font-weight: var(--font-medium);
    transition: all var(--transition-base);
}

.table-header-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.table-container {
    overflow-x: auto;
    border-radius: var(--radius-lg);
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
    .advanced-table-wrapper {
        padding: var(--space-4);
        margin: 0 -var(--space-4) var(--space-6) -var(--space-4);
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
    
    .table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-3);
    }
    
    .table-header-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .table-title {
        font-size: var(--text-lg);
    }
}

/* === TEMA SCURO === */
[data-theme="dark"] .advanced-table-wrapper {
    background: var(--neutral-800);
    border-color: var(--neutral-600);
}

[data-theme="dark"] .table-header {
    border-color: var(--neutral-600);
}

[data-theme="dark"] .table-title i {
    color: var(--primary-400);
    background: var(--neutral-700);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Inizializza la tabella avanzata quando AdvancedTables è disponibile
    function initTable() {
        if (typeof window.AdvancedTables !== 'undefined') {
            if (!window.advancedTables) {
                window.advancedTables = new AdvancedTables();
            }
            console.log('📊 Tabella avanzata <?= $tableId ?> inizializzata');
        } else {
            // Riprova dopo 100ms se AdvancedTables non è ancora caricato
            setTimeout(initTable, 100);
        }
    }
    
    initTable();
});
</script>
