<?php
/**
 * Componente Breadcrumb Dinamico
 * Studio Tecnico - Navigazione Intelligente
 */

use App\Core\Breadcrumb;

// Genera automaticamente i breadcrumb se non sono stati passati
if (!isset($breadcrumbs)) {
    $currentRoute = $_GET['route'] ?? '';
    $params = $_GET['params'] ?? [];
    $breadcrumbs = Breadcrumb::generate($currentRoute, $params);
}

// Se non ci sono breadcrumb, non renderizzare nulla
if (empty($breadcrumbs)) {
    return;
}
?>

<style>
/* === BREADCRUMB MODERNIZZATO === */
.breadcrumb-nav {
    margin-bottom: var(--space-6);
    padding: var(--space-4) 0;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border-radius: var(--radius-xl);
    border: 1px solid var(--glass-border);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-base);
}

.breadcrumb-nav:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.breadcrumb-modern {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: var(--space-2);
    margin: 0;
    padding: 0 var(--space-6);
    list-style: none;
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
}

.breadcrumb-item-modern {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    position: relative;
    transition: all var(--transition-base);
}

.breadcrumb-item-modern:not(:last-child)::after {
    content: '›';
    color: var(--neutral-400);
    font-weight: var(--font-bold);
    font-size: var(--text-base);
    margin-left: var(--space-3);
    transition: all var(--transition-base);
}

.breadcrumb-item-modern:hover:not(:last-child)::after {
    color: var(--primary-500);
    transform: translateX(2px);
}

.breadcrumb-item-modern i {
    font-size: var(--text-sm);
    color: var(--neutral-500);
    transition: all var(--transition-base);
    padding: var(--space-1);
    border-radius: var(--radius-sm);
}

.breadcrumb-item-modern:hover i {
    color: var(--primary-600);
    background: var(--primary-50);
    transform: scale(1.1);
}

.breadcrumb-item-modern a {
    color: var(--neutral-600);
    text-decoration: none;
    transition: all var(--transition-base);
    padding: var(--space-2) var(--space-3);
    border-radius: var(--radius-lg);
    position: relative;
    overflow: hidden;
}

.breadcrumb-item-modern a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    opacity: 0.1;
    transition: left var(--transition-slow);
    z-index: -1;
}

.breadcrumb-item-modern a:hover {
    color: var(--primary-700);
    background: var(--primary-50);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.breadcrumb-item-modern a:hover::before {
    left: 100%;
}

.breadcrumb-item-modern.active span {
    color: var(--text-primary);
    font-weight: var(--font-semibold);
    padding: var(--space-2) var(--space-3);
    background: var(--primary-100);
    border-radius: var(--radius-lg);
    border: 1px solid var(--primary-200);
    position: relative;
}

.breadcrumb-item-modern.active i {
    color: var(--primary-600);
    background: var(--primary-200);
}

/* === BREADCRUMB RESPONSIVE === */
@media (max-width: 768px) {
    .breadcrumb-modern {
        padding: 0 var(--space-4);
        font-size: var(--text-xs);
        gap: var(--space-1);
    }
    
    .breadcrumb-item-modern {
        gap: var(--space-1);
    }
    
    .breadcrumb-item-modern:not(:last-child)::after {
        margin-left: var(--space-2);
        font-size: var(--text-sm);
    }
    
    .breadcrumb-item-modern a,
    .breadcrumb-item-modern.active span {
        padding: var(--space-1) var(--space-2);
        font-size: var(--text-xs);
    }
    
    /* Nascondi testo su mobile, mostra solo icone per i primi elementi */
    .breadcrumb-item-modern:not(:last-child):not(:nth-last-child(2)) a {
        font-size: 0;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: var(--radius-full);
    }
    
    .breadcrumb-item-modern:not(:last-child):not(:nth-last-child(2)) i {
        font-size: var(--text-sm);
    }
}

@media (max-width: 576px) {
    .breadcrumb-nav {
        margin-bottom: var(--space-4);
        padding: var(--space-2) 0;
    }
    
    .breadcrumb-modern {
        padding: 0 var(--space-3);
    }
    
    /* Su schermi molto piccoli, mostra solo gli ultimi 2 elementi */
    .breadcrumb-item-modern:not(:nth-last-child(-n+2)) {
        display: none;
    }
    
    /* Aggiungi indicatore "..." per elementi nascosti */
    .breadcrumb-modern::before {
        content: '⋯';
        color: var(--neutral-400);
        font-weight: var(--font-bold);
        margin-right: var(--space-2);
    }
}

/* === TEMA SCURO === */
[data-theme="dark"] .breadcrumb-nav {
    background: var(--glass-bg);
    border-color: var(--glass-border);
}

[data-theme="dark"] .breadcrumb-item-modern:not(:last-child)::after {
    color: var(--neutral-500);
}

[data-theme="dark"] .breadcrumb-item-modern:hover:not(:last-child)::after {
    color: var(--primary-400);
}

[data-theme="dark"] .breadcrumb-item-modern i {
    color: var(--neutral-400);
}

[data-theme="dark"] .breadcrumb-item-modern:hover i {
    color: var(--primary-400);
    background: var(--neutral-800);
}

[data-theme="dark"] .breadcrumb-item-modern a {
    color: var(--neutral-300);
}

[data-theme="dark"] .breadcrumb-item-modern a:hover {
    color: var(--primary-300);
    background: var(--neutral-800);
}

[data-theme="dark"] .breadcrumb-item-modern.active span {
    color: var(--neutral-100);
    background: var(--neutral-800);
    border-color: var(--neutral-600);
}

[data-theme="dark"] .breadcrumb-item-modern.active i {
    color: var(--primary-400);
    background: var(--neutral-700);
}

/* === ANIMAZIONI AVANZATE === */
@keyframes breadcrumbFadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.breadcrumb-item-modern {
    animation: breadcrumbFadeIn 0.5s ease-out forwards;
    animation-delay: calc(var(--item-index, 0) * 0.1s);
    opacity: 0;
}

/* === BREADCRUMB ACTIONS === */
.breadcrumb-actions {
    margin-left: auto;
    display: flex;
    gap: var(--space-2);
    align-items: center;
}

.breadcrumb-action-btn {
    background: var(--neutral-100);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-lg);
    padding: var(--space-2);
    color: var(--neutral-600);
    text-decoration: none;
    transition: all var(--transition-base);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.breadcrumb-action-btn:hover {
    background: var(--primary-500);
    border-color: var(--primary-500);
    color: white;
    transform: scale(1.1);
    box-shadow: var(--shadow-sm);
}

.breadcrumb-action-btn i {
    font-size: var(--text-sm);
}

/* === PERFORMANCE === */
.breadcrumb-nav,
.breadcrumb-item-modern {
    will-change: transform;
    transform: translateZ(0);
}
</style>

<nav aria-label="breadcrumb" class="breadcrumb-nav">
    <ol class="breadcrumb-modern">
        <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
            <li class="breadcrumb-item-modern <?= $breadcrumb['active'] ? 'active' : '' ?>" 
                style="--item-index: <?= $index ?>">
                
                <?php if (!empty($breadcrumb['icon'])): ?>
                    <i class="<?= htmlspecialchars($breadcrumb['icon']) ?>"></i>
                <?php endif; ?>
                
                <?php if ($breadcrumb['url'] && !$breadcrumb['active']): ?>
                    <a href="<?= htmlspecialchars($breadcrumb['url']) ?>" 
                       title="Vai a <?= htmlspecialchars($breadcrumb['title']) ?>">
                        <?= htmlspecialchars($breadcrumb['title']) ?>
                    </a>
                <?php else: ?>
                    <span><?= htmlspecialchars($breadcrumb['title']) ?></span>
                <?php endif; ?>
            </li>
        <?php endforeach; ?>
        
        <!-- Azioni Breadcrumb -->
        <div class="breadcrumb-actions">
            <a href="javascript:history.back()" class="breadcrumb-action-btn" title="Indietro">
                <i class="fas fa-arrow-left"></i>
            </a>
            <a href="<?= BASE_URL ?>dashboard" class="breadcrumb-action-btn" title="Dashboard">
                <i class="fas fa-home"></i>
            </a>
            <button onclick="window.print()" class="breadcrumb-action-btn" title="Stampa">
                <i class="fas fa-print"></i>
            </button>
        </div>
    </ol>
</nav>

<script>
// === BREADCRUMB INTERACTIONS === //
document.addEventListener('DOMContentLoaded', function() {
    // Aggiungi effetti hover avanzati
    const breadcrumbItems = document.querySelectorAll('.breadcrumb-item-modern');
    
    breadcrumbItems.forEach((item, index) => {
        // Avvia animazione con delay
        setTimeout(() => {
            item.style.opacity = '1';
        }, index * 100);
        
        // Effetto hover con ripple
        item.addEventListener('mouseenter', function() {
            const link = this.querySelector('a');
            if (link) {
                link.style.transform = 'translateY(-2px) scale(1.02)';
            }
        });
        
        item.addEventListener('mouseleave', function() {
            const link = this.querySelector('a');
            if (link) {
                link.style.transform = 'translateY(0) scale(1)';
            }
        });
    });
    
    // Gestione responsive
    function handleResize() {
        const breadcrumb = document.querySelector('.breadcrumb-modern');
        if (breadcrumb && window.innerWidth <= 576) {
            // Logica per breadcrumb responsive
            const items = breadcrumb.querySelectorAll('.breadcrumb-item-modern');
            if (items.length > 2) {
                breadcrumb.classList.add('breadcrumb-collapsed');
            }
        }
    }
    
    window.addEventListener('resize', handleResize);
    handleResize(); // Esegui al caricamento
    
    console.log('🧭 Breadcrumb dinamici inizializzati');
});
</script>
