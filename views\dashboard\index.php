<?php
$pageTitle = "Dashboard";
include VIEWS_DIR . '/layouts/header.php';
?>

<style>
/* === DASHBOARD MODERNIZZATA - STUDIO TECNICO === */
.dashboard-wrapper {
    padding: var(--space-8);
    background: var(--gradient-neutral);
    min-height: calc(100vh - 80px);
    position: relative;
}

.dashboard-wrapper::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(99, 102, 241, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(16, 185, 129, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

/* === DASHBOARD CARDS MODERNE === */
.dashboard-card {
    background: var(--surface-color);
    border: 1px solid var(--neutral-200);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-base);
    overflow: hidden;
    height: 100%;
    position: relative;
    backdrop-filter: blur(10px);
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    opacity: 0;
    transition: opacity var(--transition-base);
}

.dashboard-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.03) 0%, transparent 70%);
    opacity: 0;
    transition: opacity var(--transition-base);
    pointer-events: none;
}

.dashboard-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-300);
}

.dashboard-card:hover::before {
    opacity: 1;
}

.dashboard-card:hover::after {
    opacity: 1;
}

/* === CARD HEADERS MODERNIZZATI === */
.card-header {
    background: transparent;
    border: none;
    padding: var(--space-8);
    position: relative;
    overflow: hidden;
}

/* === ICONE MODERNE CON GRADIENTS === */
.card-icon {
    width: 64px;
    height: 64px;
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--text-2xl);
    margin-bottom: var(--space-6);
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.card-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: inherit;
    opacity: 0.8;
    transition: opacity var(--transition-base);
}

.dashboard-card:hover .card-icon {
    transform: scale(1.15) rotate(5deg);
    box-shadow: var(--shadow-lg);
}

.dashboard-card:hover .card-icon::before {
    opacity: 1;
}

/* === GRADIENTS SPECIFICI PER CATEGORIA === */
.clienti .card-icon {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
}

.progetti .card-icon {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: 0 8px 20px rgba(16, 185, 129, 0.3);
}

.pratiche .card-icon {
    background: var(--gradient-accent);
    color: white;
    box-shadow: 0 8px 20px rgba(245, 158, 11, 0.3);
}

.scadenze .card-icon {
    background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%);
    color: white;
    box-shadow: 0 8px 20px rgba(239, 68, 68, 0.3);
}

/* === TITOLI MODERNIZZATI === */
.card-title {
    font-size: var(--text-lg);
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
    line-height: var(--leading-tight);
    letter-spacing: -0.025em;
}

/* === STATISTICHE CON ANIMAZIONI === */
.card-stats {
    font-size: var(--text-4xl);
    font-weight: var(--font-extrabold);
    color: var(--text-primary);
    margin: var(--space-6) 0;
    line-height: 1;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: countUp 1s ease-out;
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* === LINK MODERNIZZATI === */
.card-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-size: var(--text-sm);
    font-weight: var(--font-medium);
    display: inline-flex;
    align-items: center;
    gap: var(--space-2);
    transition: all var(--transition-base);
    padding: var(--space-2) var(--space-4);
    border-radius: var(--radius-lg);
    background: var(--neutral-100);
    border: 1px solid var(--neutral-200);
}

.card-link:hover {
    color: var(--primary-600);
    background: var(--primary-50);
    border-color: var(--primary-200);
    transform: translateX(4px);
    text-decoration: none;
}

.card-link i {
    font-size: var(--text-xs);
    transition: transform var(--transition-bounce);
}

.card-link:hover i {
    transform: translateX(4px);
}

/* === ALERT MODERNIZZATO === */
.alert {
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-xl);
    color: var(--text-primary);
    padding: var(--space-6);
    margin-bottom: var(--space-8);
}

.alert i {
    color: var(--info-color);
    font-size: var(--text-xl);
}

/* === ANIMAZIONI AVANZATE === */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.dashboard-card {
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    animation-delay: calc(var(--animation-order) * 0.15s);
    opacity: 0;
}

/* === HOVER EFFECTS AVANZATI === */
.dashboard-card:hover .card-stats {
    animation: pulse 0.6s ease-in-out;
}

/* === RESPONSIVE MIGLIORATO === */
@media (max-width: 768px) {
    .dashboard-wrapper {
        padding: var(--space-4);
    }

    .card-icon {
        width: 56px;
        height: 56px;
        font-size: var(--text-xl);
    }

    .card-stats {
        font-size: var(--text-3xl);
    }

    .card-title {
        font-size: var(--text-base);
    }
}

@media (max-width: 576px) {
    .dashboard-card {
        margin-bottom: var(--space-4);
    }

    .card-header {
        padding: var(--space-6);
    }

    .card-body {
        padding: var(--space-6);
    }
}
</style>

<div class="dashboard-wrapper">
    <?php if (!$hasStats): ?>
    <div class="alert alert-info d-flex align-items-center animate-fadeInUp" role="alert">
        <i class="fas fa-info-circle me-2"></i>
        <div>
            Dati statistici non disponibili al momento. Verifica la connessione al database o contatta l'amministratore.
        </div>
    </div>
    <?php endif; ?>

    <div class="row g-4">
        <!-- Card Clienti Modernizzata -->
        <div class="col-12 col-sm-6 col-xl-3" style="--animation-order: 1;">
            <div class="dashboard-card clienti">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="card-title">Clienti Totali</h3>
                    <div class="card-stats" data-count="<?= $stats['total_clients'] ?>"><?= number_format($stats['total_clients']) ?></div>
                </div>
                <div class="card-body">
                    <a href="<?= BASE_URL ?>clienti" class="card-link">
                        <span>Gestisci Clienti</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Card Progetti Modernizzata -->
        <div class="col-12 col-sm-6 col-xl-3" style="--animation-order: 2;">
            <div class="dashboard-card progetti">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-project-diagram"></i>
                    </div>
                    <h3 class="card-title">Progetti Totali</h3>
                    <div class="card-stats" data-count="<?= $stats['total_projects'] ?>"><?= number_format($stats['total_projects']) ?></div>
                </div>
                <div class="card-body">
                    <a href="<?= BASE_URL ?>progetti" class="card-link">
                        <span>Gestisci Progetti</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Card Pratiche Attive Modernizzata -->
        <div class="col-12 col-sm-6 col-xl-3" style="--animation-order: 3;">
            <div class="dashboard-card pratiche">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3 class="card-title">Pratiche Attive</h3>
                    <div class="card-stats" data-count="<?= $stats['active_practices'] ?>"><?= number_format($stats['active_practices']) ?></div>
                </div>
                <div class="card-body">
                    <a href="<?= BASE_URL ?>pratiche" class="card-link">
                        <span>Gestisci Pratiche</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Card Scadenze Modernizzata -->
        <div class="col-12 col-sm-6 col-xl-3" style="--animation-order: 4;">
            <div class="dashboard-card scadenze">
                <div class="card-header">
                    <div class="card-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h3 class="card-title">Scadenze in Arrivo</h3>
                    <div class="card-stats" data-count="<?= $stats['pending_deadlines'] ?>"><?= number_format($stats['pending_deadlines']) ?></div>
                </div>
                <div class="card-body">
                    <a href="<?= BASE_URL ?>pratiche?filter=scadenze" class="card-link">
                        <span>Vedi Scadenze</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <?php if ($hasStats): ?>
    <!-- Sezione Grafici Modernizzata -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="dashboard-card chart-section" style="--animation-order: 5;">
                <div class="card-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <div>
                            <h3 class="card-title mb-2">Andamento Attività</h3>
                            <p class="text-secondary mb-0">Panoramica delle performance mensili</p>
                        </div>
                        <div class="chart-controls">
                            <button class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-chart-line me-2"></i>
                                Visualizza Report
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-placeholder">
                        <div class="chart-icon">
                            <i class="fas fa-chart-area"></i>
                        </div>
                        <h4>Grafici in Arrivo</h4>
                        <p class="text-secondary">I grafici delle performance saranno disponibili nella prossima versione</p>
                        <div class="chart-preview">
                            <div class="chart-bar" style="height: 60%; --delay: 0.1s;"></div>
                            <div class="chart-bar" style="height: 80%; --delay: 0.2s;"></div>
                            <div class="chart-bar" style="height: 45%; --delay: 0.3s;"></div>
                            <div class="chart-bar" style="height: 90%; --delay: 0.4s;"></div>
                            <div class="chart-bar" style="height: 70%; --delay: 0.5s;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
    /* === SEZIONE GRAFICI === */
    .chart-section {
        background: var(--glass-bg);
        backdrop-filter: var(--glass-backdrop);
        border: 1px solid var(--glass-border);
    }

    .chart-placeholder {
        text-align: center;
        padding: var(--space-12) var(--space-8);
        color: var(--text-secondary);
    }

    .chart-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto var(--space-6);
        background: var(--gradient-primary);
        border-radius: var(--radius-full);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: var(--text-3xl);
        color: white;
        box-shadow: var(--shadow-lg);
    }

    .chart-preview {
        display: flex;
        align-items: end;
        justify-content: center;
        gap: var(--space-2);
        margin-top: var(--space-8);
        height: 100px;
    }

    .chart-bar {
        width: 20px;
        background: var(--gradient-primary);
        border-radius: var(--radius-sm) var(--radius-sm) 0 0;
        animation: chartGrow 1s ease-out forwards;
        animation-delay: var(--delay);
        transform: scaleY(0);
        transform-origin: bottom;
    }

    @keyframes chartGrow {
        to {
            transform: scaleY(1);
        }
    }

    .chart-controls .btn {
        border-radius: var(--radius-lg);
        font-weight: var(--font-medium);
    }
    </style>
    <?php endif; ?>
</div>

<!-- Script per Animazioni Dashboard -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // === ANIMAZIONE CONTATORI === //
    function animateCounters() {
        const counters = document.querySelectorAll('.card-stats[data-count]');

        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-count'));
            const duration = 2000; // 2 secondi
            const increment = target / (duration / 16); // 60fps
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                // Formatta il numero con separatori delle migliaia
                counter.textContent = Math.floor(current).toLocaleString('it-IT');
            }, 16);
        });
    }

    // === OBSERVER PER ANIMAZIONI AL SCROLL === //
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';

                // Avvia animazione contatori quando le cards sono visibili
                if (entry.target.classList.contains('dashboard-card')) {
                    setTimeout(() => {
                        animateCounters();
                    }, 500);
                }
            }
        });
    }, observerOptions);

    // Osserva tutte le dashboard cards
    document.querySelectorAll('.dashboard-card').forEach(card => {
        observer.observe(card);
    });

    // === EFFETTI HOVER AVANZATI === //
    document.querySelectorAll('.dashboard-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            // Aggiungi effetto parallax leggero
            const icon = this.querySelector('.card-icon');
            if (icon) {
                icon.style.transform = 'scale(1.15) rotate(5deg) translateZ(0)';
            }
        });

        card.addEventListener('mouseleave', function() {
            const icon = this.querySelector('.card-icon');
            if (icon) {
                icon.style.transform = 'scale(1) rotate(0deg) translateZ(0)';
            }
        });

        // Effetto click con ripple
        card.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            ripple.classList.add('ripple-effect');

            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // === TEMA DINAMICO === //
    function updateDashboardTheme() {
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
        const cards = document.querySelectorAll('.dashboard-card');

        cards.forEach(card => {
            if (isDark) {
                card.style.boxShadow = 'var(--shadow-lg)';
            } else {
                card.style.boxShadow = 'var(--shadow-md)';
            }
        });
    }

    // Osserva cambiamenti di tema
    const themeObserver = new MutationObserver(updateDashboardTheme);
    themeObserver.observe(document.documentElement, {
        attributes: true,
        attributeFilter: ['data-theme']
    });

    // === PERFORMANCE MONITORING === //
    if ('performance' in window) {
        window.addEventListener('load', () => {
            const loadTime = performance.now();
            console.log(`🚀 Dashboard caricata in ${Math.round(loadTime)}ms`);
        });
    }
});
</script>

<style>
/* === EFFETTI RIPPLE === */
.ripple-effect {
    position: absolute;
    border-radius: 50%;
    background: rgba(99, 102, 241, 0.3);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* === OTTIMIZZAZIONI PERFORMANCE === */
.dashboard-card {
    will-change: transform, box-shadow;
    transform: translateZ(0); /* Forza accelerazione hardware */
}

.card-icon {
    will-change: transform;
    transform: translateZ(0);
}

/* === PRELOAD ANIMATIONS === */
.dashboard-card {
    animation-play-state: paused;
}
</style>

<?php include VIEWS_DIR . '/layouts/footer.php'; ?>
